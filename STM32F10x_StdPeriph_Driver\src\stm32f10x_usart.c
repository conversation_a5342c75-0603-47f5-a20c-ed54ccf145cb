#include "stm32f10x_usart.h"
#include "stm32f10x_rcc.h"

// USART初始化
void USART_Init(USART_TypeDef* USARTx, USART_InitTypeDef* USART_InitStruct) {
    uint32_t tmpreg = 0x00, apbclock = 0x00;
    uint32_t integerdivider = 0x00;
    uint32_t fractionaldivider = 0x00;
    RCC_ClocksTypeDef RCC_ClocksStatus;
    
    // 配置USART字长、奇偶校验和模式
    tmpreg = USARTx->CR1;
    tmpreg &= (uint32_t)~((uint32_t)0x1000 | 0x0400 | 0x000C); // 清除M, PCE, PS, TE和RE位
    tmpreg |= (uint32_t)USART_InitStruct->USART_WordLength | USART_InitStruct->USART_Parity |
              USART_InitStruct->USART_Mode;
    USARTx->CR1 = (uint16_t)tmpreg;
    
    // 配置USART停止位
    tmpreg = USARTx->CR2;
    tmpreg &= (uint32_t)~((uint32_t)0x3000); // 清除STOP[13:12]位
    tmpreg |= (uint32_t)USART_InitStruct->USART_StopBits;
    USARTx->CR2 = (uint16_t)tmpreg;
    
    // 配置USART硬件流控制
    tmpreg = USARTx->CR3;
    tmpreg &= (uint32_t)~((uint32_t)0x0300); // 清除CTSE和RTSE位
    tmpreg |= USART_InitStruct->USART_HardwareFlowControl;
    USARTx->CR3 = (uint16_t)tmpreg;
    
    // 配置USART波特率
    RCC_GetClocksFreq(&RCC_ClocksStatus);
    
    if (USARTx == USART1) {
        apbclock = RCC_ClocksStatus.PCLK2_Frequency;
    } else {
        apbclock = RCC_ClocksStatus.PCLK1_Frequency;
    }
    
    // 确定整数部分
    if ((USARTx->CR1 & 0x8000) != 0) {
        // 如果过采样为8
        integerdivider = ((25 * apbclock) / (2 * (USART_InitStruct->USART_BaudRate)));
    } else {
        // 如果过采样为16
        integerdivider = ((25 * apbclock) / (4 * (USART_InitStruct->USART_BaudRate)));
    }
    tmpreg = (integerdivider / 100) << 4;
    
    // 确定小数部分
    fractionaldivider = integerdivider - (100 * (tmpreg >> 4));
    
    if ((USARTx->CR1 & 0x8000) != 0) {
        tmpreg |= ((((fractionaldivider * 8) + 50) / 100)) & ((uint8_t)0x07);
    } else {
        tmpreg |= ((((fractionaldivider * 16) + 50) / 100)) & ((uint8_t)0x0F);
    }
    
    USARTx->BRR = (uint16_t)tmpreg;
}

// 使能或禁用USART
void USART_Cmd(USART_TypeDef* USARTx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        USARTx->CR1 |= 0x2000;
    } else {
        USARTx->CR1 &= (uint16_t)~((uint16_t)0x2000);
    }
}

// 发送数据
void USART_SendData(USART_TypeDef* USARTx, uint16_t Data) {
    USARTx->DR = (Data & (uint16_t)0x01FF);
}

// 接收数据
uint16_t USART_ReceiveData(USART_TypeDef* USARTx) {
    return (uint16_t)(USARTx->DR & (uint16_t)0x01FF);
}

// 获取标志状态
FlagStatus USART_GetFlagStatus(USART_TypeDef* USARTx, uint16_t USART_FLAG) {
    FlagStatus bitstatus = RESET;
    
    if ((USARTx->SR & USART_FLAG) != (uint16_t)RESET) {
        bitstatus = SET;
    } else {
        bitstatus = RESET;
    }
    return bitstatus;
}

// 清除标志
void USART_ClearFlag(USART_TypeDef* USARTx, uint16_t USART_FLAG) {
    USARTx->SR = (uint16_t)~USART_FLAG;
}

// USART结构体初始化
void USART_StructInit(USART_InitTypeDef* USART_InitStruct) {
    USART_InitStruct->USART_BaudRate = 9600;
    USART_InitStruct->USART_WordLength = USART_WordLength_8b;
    USART_InitStruct->USART_StopBits = USART_StopBits_1;
    USART_InitStruct->USART_Parity = USART_Parity_No;
    USART_InitStruct->USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStruct->USART_HardwareFlowControl = USART_HardwareFlowControl_None;
}

// USART复位
void USART_DeInit(USART_TypeDef* USARTx) {
    if (USARTx == USART1) {
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_USART1, ENABLE);
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_USART1, DISABLE);
    }
}
