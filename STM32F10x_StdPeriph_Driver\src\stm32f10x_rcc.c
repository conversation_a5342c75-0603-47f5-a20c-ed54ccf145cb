#include "stm32f10x_rcc.h"

// 使能或禁用APB2外设时钟
void RCC_APB2PeriphClockCmd(uint32_t RCC_APB2Periph, FunctionalState NewState) {
    if (NewState != DISABLE) {
        RCC->APB2ENR |= RCC_APB2Periph;
    } else {
        RCC->APB2ENR &= ~RCC_APB2Periph;
    }
}

// 使能或禁用APB1外设时钟
void RCC_APB1PeriphClockCmd(uint32_t RCC_APB1Periph, FunctionalState NewState) {
    if (NewState != DISABLE) {
        RCC->APB1ENR |= RCC_APB1Periph;
    } else {
        RCC->APB1ENR &= ~RCC_APB1Periph;
    }
}

// 强制或释放APB2外设复位
void RCC_APB2PeriphResetCmd(uint32_t RCC_APB2Periph, FunctionalState NewState) {
    if (NewState != DISABLE) {
        RCC->APB2RSTR |= RCC_APB2Periph;
    } else {
        RCC->APB2RSTR &= ~RCC_APB2Periph;
    }
}

// 强制或释放APB1外设复位
void RCC_APB1PeriphResetCmd(uint32_t RCC_APB1Periph, FunctionalState NewState) {
    if (NewState != DISABLE) {
        RCC->APB1RSTR |= RCC_APB1Periph;
    } else {
        RCC->APB1RSTR &= ~RCC_APB1Periph;
    }
}

// RCC复位
void RCC_DeInit(void) {
    RCC->CR |= (uint32_t)0x00000001;         // 设置HSION位
    RCC->CFGR &= (uint32_t)0xF8FF0000;       // 复位SW, HPRE, PPRE1, PPRE2, ADCPRE和MCO位
    RCC->CR &= (uint32_t)0xFEF6FFFF;         // 复位HSEON, CSSON和PLLON位
    RCC->CR &= (uint32_t)0xFFFBFFFF;         // 复位HSEBYP位
    RCC->CFGR &= (uint32_t)0xFF80FFFF;       // 复位PLLSRC, PLLXTPRE, PLLMUL和USBPRE位
    RCC->CIR = 0x009F0000;                   // 禁用所有中断并清除挂起位
}

// 配置HSE
void RCC_HSEConfig(uint32_t RCC_HSE) {
    RCC->CR &= ~((uint32_t)(0x00010000 | 0x00040000)); // 清除HSEON和HSEBYP位
    RCC->CR |= RCC_HSE;
}

// 等待HSE就绪
ErrorStatus RCC_WaitForHSEStartUp(void) {
    __IO uint32_t StartUpCounter = 0;
    ErrorStatus status = ERROR;
    FlagStatus HSEStatus = RESET;
    
    do {
        HSEStatus = (RCC->CR & 0x00020000) ? SET : RESET; // 检查HSERDY位
        StartUpCounter++;
    } while((StartUpCounter != 0x0500) && (HSEStatus == RESET));
    
    if ((RCC->CR & 0x00020000) != RESET) {
        status = SUCCESS;
    } else {
        status = ERROR;
    }
    
    return (status);
}

// 配置PLL
void RCC_PLLConfig(uint32_t RCC_PLLSource, uint32_t RCC_PLLMul) {
    uint32_t tmpreg = 0;
    
    tmpreg = RCC->CFGR;
    tmpreg &= ~(0x00010000 | 0x00020000 | 0x003C0000); // 清除PLLSRC, PLLXTPRE和PLLMUL位
    tmpreg |= RCC_PLLSource | RCC_PLLMul;
    RCC->CFGR = tmpreg;
}

// 使能或禁用PLL
void RCC_PLLCmd(FunctionalState NewState) {
    if (NewState != DISABLE) {
        RCC->CR |= 0x01000000; // 设置PLLON位
    } else {
        RCC->CR &= ~0x01000000; // 清除PLLON位
    }
}

// 配置系统时钟
void RCC_SYSCLKConfig(uint32_t RCC_SYSCLKSource) {
    uint32_t tmpreg = 0;
    
    tmpreg = RCC->CFGR;
    tmpreg &= ~0x00000003; // 清除SW位
    tmpreg |= RCC_SYSCLKSource;
    RCC->CFGR = tmpreg;
}

// 获取系统时钟源
uint8_t RCC_GetSYSCLKSource(void) {
    return ((uint8_t)(RCC->CFGR & 0x0000000C));
}

// 配置AHB时钟
void RCC_HCLKConfig(uint32_t RCC_SYSCLK) {
    uint32_t tmpreg = 0;
    
    tmpreg = RCC->CFGR;
    tmpreg &= ~0x000000F0; // 清除HPRE位
    tmpreg |= RCC_SYSCLK;
    RCC->CFGR = tmpreg;
}

// 配置APB1时钟
void RCC_PCLK1Config(uint32_t RCC_HCLK) {
    uint32_t tmpreg = 0;
    
    tmpreg = RCC->CFGR;
    tmpreg &= ~0x00000700; // 清除PPRE1位
    tmpreg |= RCC_HCLK;
    RCC->CFGR = tmpreg;
}

// 配置APB2时钟
void RCC_PCLK2Config(uint32_t RCC_HCLK) {
    uint32_t tmpreg = 0;
    
    tmpreg = RCC->CFGR;
    tmpreg &= ~0x00003800; // 清除PPRE2位
    tmpreg |= RCC_HCLK;
    RCC->CFGR = tmpreg;
}

// 获取时钟频率
void RCC_GetClocksFreq(RCC_ClocksTypeDef* RCC_Clocks) {
    uint32_t tmp = 0, pllmull = 0, pllsource = 0, presc = 0;
    
    // 获取SYSCLK源
    tmp = RCC->CFGR & 0x0000000C;
    
    switch (tmp) {
        case 0x00:  // HSI作为系统时钟
            RCC_Clocks->SYSCLK_Frequency = 8000000;
            break;
        case 0x04:  // HSE作为系统时钟
            RCC_Clocks->SYSCLK_Frequency = 8000000;
            break;
        case 0x08:  // PLL作为系统时钟
            pllmull = RCC->CFGR & 0x003C0000;
            pllsource = RCC->CFGR & 0x00010000;
            pllmull = (pllmull >> 18) + 2;
            
            if (pllsource == 0x00) {
                RCC_Clocks->SYSCLK_Frequency = (8000000 >> 1) * pllmull;
            } else {
                if ((RCC->CFGR & 0x00020000) != 0) {
                    RCC_Clocks->SYSCLK_Frequency = (8000000 >> 1) * pllmull;
                } else {
                    RCC_Clocks->SYSCLK_Frequency = 8000000 * pllmull;
                }
            }
            break;
        default:
            RCC_Clocks->SYSCLK_Frequency = 8000000;
            break;
    }
    
    // 计算HCLK, PCLK1和PCLK2频率
    tmp = RCC->CFGR & 0x000000F0;
    tmp = tmp >> 4;
    presc = (tmp & 0x08) ? ((tmp & 0x07) + 1) : 0;
    RCC_Clocks->HCLK_Frequency = RCC_Clocks->SYSCLK_Frequency >> presc;
    
    tmp = RCC->CFGR & 0x00000700;
    tmp = tmp >> 8;
    presc = (tmp & 0x04) ? ((tmp & 0x03) + 1) : 0;
    RCC_Clocks->PCLK1_Frequency = RCC_Clocks->HCLK_Frequency >> presc;
    
    tmp = RCC->CFGR & 0x00003800;
    tmp = tmp >> 11;
    presc = (tmp & 0x04) ? ((tmp & 0x03) + 1) : 0;
    RCC_Clocks->PCLK2_Frequency = RCC_Clocks->HCLK_Frequency >> presc;
    
    RCC_Clocks->ADCCLK_Frequency = RCC_Clocks->PCLK2_Frequency;
}
