#include "config.h"
#include "mpu6050.h"
#include "servo.h"
#include <stdio.h>
#include <math.h>

// 全局变量
MPU6050_Data mpu_data = {0}; // MPU6050数据
Servo_Control servo = {0};   // 舵机控制
uint8_t system_status = 0;   // 系统状态

// 系统初始化
static void System_Init(void) {
    // 配置系统时钟为72MHz
    SystemInit();
    
    // 初始化SysTick定时器(1ms中断)
    SysTick_Config(SYSTEM_CLOCK_FREQ / 1000);
    
    // 初始化状态LED
    GPIO_InitTypeDef GPIO_InitStructure;
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
    GPIO_InitStructure.GPIO_Pin = STATUS_LED_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
    GPIO_Init(STATUS_LED_PORT, &GPIO_InitStructure);
    
    #if DEBUG_UART_ENABLE
    // 初始化调试串口
    USART_InitTypeDef USART_InitStructure;
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
    
    GPIO_InitStructure.GPIO_Pin = DEBUG_UART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(DEBUG_UART_PORT, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = DEBUG_UART_RX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(DEBUG_UART_PORT, &GPIO_InitStructure);
    
    USART_InitStructure.USART_BaudRate = DEBUG_UART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(DEBUG_UART, &USART_InitStructure);
    USART_Cmd(DEBUG_UART, ENABLE);
    #endif
}

// 延时函数
static void Delay_ms(uint32_t ms) {
    for(volatile uint32_t i = 0; i < ms * 8000; i++); // 简单延时
}

// LED状态指示
static void LED_Toggle(void) {
    static uint8_t led_state = 0;
    led_state = !led_state;
    if(led_state) GPIO_SetBits(STATUS_LED_PORT, STATUS_LED_PIN);
    else GPIO_ResetBits(STATUS_LED_PORT, STATUS_LED_PIN);
}

// 角度跟随控制算法
static void Angle_Follow_Control(void) {
    static float last_angle = 0;
    float current_angle = mpu_data.angle_x * ANGLE_SCALE_FACTOR; // 使用X轴角度

    // 角度死区处理
    if(fabsf(current_angle - last_angle) < ANGLE_DEADZONE) return;

    // 角度范围映射到舵机范围
    float servo_angle = current_angle + 90.0f; // 将±90°映射到0-180°
    if(servo_angle < SERVO_ANGLE_MIN) servo_angle = SERVO_ANGLE_MIN;
    if(servo_angle > SERVO_ANGLE_MAX) servo_angle = SERVO_ANGLE_MAX;

    Servo_SetAngle(&servo, servo_angle);
    Servo_Update(&servo);

    last_angle = current_angle;

    #if DEBUG_UART_ENABLE
    // 发送调试信息(简化版本，避免使用sprintf)
    static uint32_t debug_counter = 0;
    if(++debug_counter >= 50) { // 每500ms输出一次
        debug_counter = 0;
        char msg[] = "Angle Follow OK\r\n";
        for(int i = 0; msg[i]; i++) {
            while(USART_GetFlagStatus(DEBUG_UART, USART_FLAG_TC) == RESET);
            USART_SendData(DEBUG_UART, msg[i]);
        }
    }
    #endif
}

// 主函数
int main(void) {
    System_Init(); // 系统初始化
    
    // 初始化MPU6050
    if(MPU6050_Init() != ERROR_NONE) {
        system_status = ERROR_MPU6050_INIT;
        while(1) { LED_Toggle(); Delay_ms(100); } // 快速闪烁表示错误
    }
    
    // 初始化舵机
    if(Servo_Init() != ERROR_NONE) {
        system_status = ERROR_SERVO_INIT;
        while(1) { LED_Toggle(); Delay_ms(200); } // 中速闪烁表示错误
    }
    
    // 校准MPU6050
    LED_Toggle(); // 指示开始校准
    MPU6050_Calibrate(&mpu_data);
    LED_Toggle(); // 指示校准完成
    
    // 使能舵机
    Servo_Enable(&servo);
    Servo_SetAngle(&servo, 90.0f); // 初始位置90度
    Servo_Update(&servo);
    
    system_status = ERROR_NONE;
    
    // 主循环
    while(1) {
        // 读取MPU6050数据
        if(MPU6050_ReadData(&mpu_data) == ERROR_NONE) {
            MPU6050_CalcAngles(&mpu_data); // 计算角度
            Angle_Follow_Control();        // 角度跟随控制
            LED_Toggle();                  // 正常工作指示
        }
        
        Delay_ms(CONTROL_LOOP_DELAY); // 控制循环延时
    }
}
