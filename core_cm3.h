#ifndef __CORE_CM3_H_GENERIC
#define __CORE_CM3_H_GENERIC

#ifdef __cplusplus
 extern "C" {
#endif

// CMSIS版本信息
#define __CM3_CMSIS_VERSION_MAIN  (0x03)
#define __CM3_CMSIS_VERSION_SUB   (0x20)
#define __CM3_CMSIS_VERSION       ((__CM3_CMSIS_VERSION_MAIN << 16) | __CM3_CMSIS_VERSION_SUB)

// 处理器和核心外设访问函数
#define __CORTEX_M                (0x03)

#include <stdint.h>

#ifndef __ASM
  #define __ASM                                  __asm
#endif
#ifndef __INLINE
  #define __INLINE                               inline
#endif
#ifndef __STATIC_INLINE
  #define __STATIC_INLINE                        static inline
#endif
#ifndef __STATIC_FORCEINLINE                     
  #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
#endif                                           
#ifndef __NO_RETURN
  #define __NO_RETURN                            __attribute__((__noreturn__))
#endif
#ifndef __USED
  #define __USED                                 __attribute__((used))
#endif
#ifndef __WEAK
  #define __WEAK                                 __attribute__((weak))
#endif
#ifndef __PACKED
  #define __PACKED                               __attribute__((packed))
#endif
#ifndef __PACKED_STRUCT
  #define __PACKED_STRUCT                        struct __attribute__((packed))
#endif
#ifndef __PACKED_UNION
  #define __PACKED_UNION                         union __attribute__((packed))
#endif
#ifndef __UNALIGNED_UINT32        /* deprecated */
  #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32_READ *)(x))->v)
#endif
#ifndef __UNALIGNED_UINT16_READ
  #define __UNALIGNED_UINT16_READ(addr)          ((uint16_t)*((uint16_t*)(addr)))
#endif
#ifndef __UNALIGNED_UINT16_WRITE
  #define __UNALIGNED_UINT16_WRITE(addr, val)    ((*(uint16_t*)(addr)) = (val))
#endif
#ifndef __UNALIGNED_UINT32_READ
  #define __UNALIGNED_UINT32_READ(addr)          ((uint32_t)*((uint32_t*)(addr)))
#endif
#ifndef __UNALIGNED_UINT32_WRITE
  #define __UNALIGNED_UINT32_WRITE(addr, val)    ((*(uint32_t*)(addr)) = (val))
#endif
#ifndef __ALIGNED
  #define __ALIGNED(x)                           __attribute__((aligned(x)))
#endif
#ifndef __RESTRICT
  #define __RESTRICT                             __restrict
#endif

// IO定义
#ifdef __cplusplus
  #define   __I     volatile             /*!< Defines 'read only' permissions */
#else
  #define   __I     volatile const       /*!< Defines 'read only' permissions */
#endif
#define     __O     volatile             /*!< Defines 'write only' permissions */
#define     __IO    volatile             /*!< Defines 'read / write' permissions */

// 中断号定义
typedef enum IRQn {
  NonMaskableInt_IRQn         = -14,    /*!< 2 Non Maskable Interrupt                             */
  MemoryManagement_IRQn       = -12,    /*!< 4 Cortex-M3 Memory Management Interrupt              */
  BusFault_IRQn               = -11,    /*!< 5 Cortex-M3 Bus Fault Interrupt                      */
  UsageFault_IRQn             = -10,    /*!< 6 Cortex-M3 Usage Fault Interrupt                    */
  SVCall_IRQn                 = -5,     /*!< 11 Cortex-M3 SV Call Interrupt                       */
  DebugMonitor_IRQn           = -4,     /*!< 12 Cortex-M3 Debug Monitor Interrupt                 */
  PendSV_IRQn                 = -2,     /*!< 14 Cortex-M3 Pend SV Interrupt                       */
  SysTick_IRQn                = -1,     /*!< 15 Cortex-M3 System Tick Interrupt                   */

  WWDG_IRQn                   = 0,      /*!< Window WatchDog Interrupt                             */
  PVD_IRQn                    = 1,      /*!< PVD through EXTI Line detection Interrupt            */
  TAMPER_IRQn                 = 2,      /*!< Tamper Interrupt                                      */
  RTC_IRQn                    = 3,      /*!< RTC global Interrupt                                  */
  FLASH_IRQn                  = 4,      /*!< FLASH global Interrupt                                */
  RCC_IRQn                    = 5,      /*!< RCC global Interrupt                                  */
  EXTI0_IRQn                  = 6,      /*!< EXTI Line0 Interrupt                                  */
  EXTI1_IRQn                  = 7,      /*!< EXTI Line1 Interrupt                                  */
  EXTI2_IRQn                  = 8,      /*!< EXTI Line2 Interrupt                                  */
  EXTI3_IRQn                  = 9,      /*!< EXTI Line3 Interrupt                                  */
  EXTI4_IRQn                  = 10,     /*!< EXTI Line4 Interrupt                                  */
  DMA1_Channel1_IRQn          = 11,     /*!< DMA1 Channel 1 global Interrupt                      */
  DMA1_Channel2_IRQn          = 12,     /*!< DMA1 Channel 2 global Interrupt                      */
  DMA1_Channel3_IRQn          = 13,     /*!< DMA1 Channel 3 global Interrupt                      */
  DMA1_Channel4_IRQn          = 14,     /*!< DMA1 Channel 4 global Interrupt                      */
  DMA1_Channel5_IRQn          = 15,     /*!< DMA1 Channel 5 global Interrupt                      */
  DMA1_Channel6_IRQn          = 16,     /*!< DMA1 Channel 6 global Interrupt                      */
  DMA1_Channel7_IRQn          = 17,     /*!< DMA1 Channel 7 global Interrupt                      */
  ADC1_2_IRQn                 = 18,     /*!< ADC1 and ADC2 global Interrupt                       */
  USB_HP_CAN1_TX_IRQn         = 19,     /*!< USB Device High Priority or CAN1 TX Interrupts       */
  USB_LP_CAN1_RX0_IRQn        = 20,     /*!< USB Device Low Priority or CAN1 RX0 Interrupts       */
  CAN1_RX1_IRQn               = 21,     /*!< CAN1 RX1 Interrupt                                    */
  CAN1_SCE_IRQn               = 22,     /*!< CAN1 SCE Interrupt                                    */
  EXTI9_5_IRQn                = 23,     /*!< External Line[9:5] Interrupts                        */
  TIM1_BRK_IRQn               = 24,     /*!< TIM1 Break Interrupt                                  */
  TIM1_UP_IRQn                = 25,     /*!< TIM1 Update Interrupt                                 */
  TIM1_TRG_COM_IRQn           = 26,     /*!< TIM1 Trigger and Commutation Interrupt               */
  TIM1_CC_IRQn                = 27,     /*!< TIM1 Capture Compare Interrupt                        */
  TIM2_IRQn                   = 28,     /*!< TIM2 global Interrupt                                 */
  TIM3_IRQn                   = 29,     /*!< TIM3 global Interrupt                                 */
  TIM4_IRQn                   = 30,     /*!< TIM4 global Interrupt                                 */
  I2C1_EV_IRQn                = 31,     /*!< I2C1 Event Interrupt                                  */
  I2C1_ER_IRQn                = 32,     /*!< I2C1 Error Interrupt                                  */
  I2C2_EV_IRQn                = 33,     /*!< I2C2 Event Interrupt                                  */
  I2C2_ER_IRQn                = 34,     /*!< I2C2 Error Interrupt                                  */
  SPI1_IRQn                   = 35,     /*!< SPI1 global Interrupt                                 */
  SPI2_IRQn                   = 36,     /*!< SPI2 global Interrupt                                 */
  USART1_IRQn                 = 37,     /*!< USART1 global Interrupt                               */
  USART2_IRQn                 = 38,     /*!< USART2 global Interrupt                               */
  USART3_IRQn                 = 39,     /*!< USART3 global Interrupt                               */
  EXTI15_10_IRQn              = 40,     /*!< External Line[15:10] Interrupts                      */
  RTCAlarm_IRQn               = 41,     /*!< RTC Alarm through EXTI Line Interrupt                */
  USBWakeUp_IRQn              = 42      /*!< USB Device WakeUp from suspend through EXTI Line Interrupt */
} IRQn_Type;

// 配置处理器和核心外设
#define __NVIC_PRIO_BITS          4               /*!< STM32 uses 4 Bits for the Priority Levels    */
#define __Vendor_SysTickConfig    0               /*!< Set to 1 if different SysTick Config is used */

// SysTick结构体定义
typedef struct {
  __IO uint32_t CTRL;                   /*!< Offset: 0x000 (R/W)  SysTick Control and Status Register */
  __IO uint32_t LOAD;                   /*!< Offset: 0x004 (R/W)  SysTick Reload Value Register       */
  __IO uint32_t VAL;                    /*!< Offset: 0x008 (R/W)  SysTick Current Value Register      */
  __I  uint32_t CALIB;                  /*!< Offset: 0x00C (R/ )  SysTick Calibration Register        */
} SysTick_Type;

// SysTick基地址
#define SCS_BASE            (0xE000E000UL)                            /*!< System Control Space Base Address  */
#define SysTick_BASE        (SCS_BASE +  0x0010UL)                    /*!< SysTick Base Address                */

#define SysTick             ((SysTick_Type   *)     SysTick_BASE  )   /*!< SysTick configuration struct       */

// 核心函数声明
extern uint32_t SystemCoreClock;     /*!< System Clock Frequency (Core Clock)  */

extern void SystemInit(void);
extern void SystemCoreClockUpdate(void);

// SysTick函数
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks) {
  if ((ticks - 1UL) > 0xFFFFFFUL) {
    return (1UL);                                                   /* Reload value impossible */
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
  SysTick->VAL   = 0UL;                                              /* Load the SysTick Counter Value */
  SysTick->CTRL  = (1UL << 2) |
                   (1UL << 1) |
                   (1UL << 0);                                       /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
}

#ifdef __cplusplus
}
#endif

#endif /* __CORE_CM3_H_GENERIC */
