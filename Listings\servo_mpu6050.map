Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.Angle_Follow_Control) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.Angle_Follow_Control) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    main.o(i.Angle_Follow_Control) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    main.o(i.Angle_Follow_Control) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    main.o(i.Angle_Follow_Control) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.Angle_Follow_Control) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    main.o(i.Angle_Follow_Control) refers to servo.o(i.Servo_Update) for Servo_Update
    main.o(i.Angle_Follow_Control) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.Angle_Follow_Control) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    main.o(i.Angle_Follow_Control) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    main.o(i.Angle_Follow_Control) refers to main.o(.bss) for mpu_data
    main.o(i.Angle_Follow_Control) refers to main.o(.data) for last_angle
    main.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.LED_Toggle) refers to main.o(.data) for led_state
    main.o(i.System_Init) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.System_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    main.o(i.System_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.System_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    main.o(i.System_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    main.o(i.main) refers to main.o(i.System_Init) for System_Init
    main.o(i.main) refers to mpu6050.o(i.MPU6050_Init) for MPU6050_Init
    main.o(i.main) refers to main.o(i.LED_Toggle) for LED_Toggle
    main.o(i.main) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to servo.o(i.Servo_Init) for Servo_Init
    main.o(i.main) refers to mpu6050.o(i.MPU6050_Calibrate) for MPU6050_Calibrate
    main.o(i.main) refers to servo.o(i.Servo_Enable) for Servo_Enable
    main.o(i.main) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    main.o(i.main) refers to servo.o(i.Servo_Update) for Servo_Update
    main.o(i.main) refers to mpu6050.o(i.MPU6050_ReadData) for MPU6050_ReadData
    main.o(i.main) refers to mpu6050.o(i.MPU6050_CalcAngles) for MPU6050_CalcAngles
    main.o(i.main) refers to main.o(i.Angle_Follow_Control) for Angle_Follow_Control
    main.o(i.main) refers to main.o(.data) for system_status
    main.o(i.main) refers to main.o(.bss) for mpu_data
    mpu6050.o(i.MPU6050_CalcAngles) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    mpu6050.o(i.MPU6050_CalcAngles) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    mpu6050.o(i.MPU6050_CalcAngles) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    mpu6050.o(i.MPU6050_CalcAngles) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    mpu6050.o(i.MPU6050_CalcAngles) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    mpu6050.o(i.MPU6050_CalcAngles) refers to atan2f.o(i.atan2f) for atan2f
    mpu6050.o(i.MPU6050_CalcAngles) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(i.MPU6050_CalcAngles) refers to sqrtf.o(i.sqrtf) for sqrtf
    mpu6050.o(i.MPU6050_CalcAngles) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    mpu6050.o(i.MPU6050_CalcAngles) refers to mpu6050.o(.data) for last_time
    mpu6050.o(i.MPU6050_Calibrate) refers to mpu6050.o(i.MPU6050_ReadData) for MPU6050_ReadData
    mpu6050.o(i.MPU6050_Calibrate) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    mpu6050.o(i.MPU6050_Calibrate) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    mpu6050.o(i.MPU6050_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    mpu6050.o(i.MPU6050_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    mpu6050.o(i.MPU6050_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    mpu6050.o(i.MPU6050_I2C_Init) refers to stm32f10x_i2c.o(i.I2C_Init) for I2C_Init
    mpu6050.o(i.MPU6050_I2C_Init) refers to stm32f10x_i2c.o(i.I2C_Cmd) for I2C_Cmd
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU6050_I2C_Init) for MPU6050_I2C_Init
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU6050_WriteReg) for MPU6050_WriteReg
    mpu6050.o(i.MPU6050_ReadData) refers to mpu6050.o(i.MPU6050_ReadMultiReg) for MPU6050_ReadMultiReg
    mpu6050.o(i.MPU6050_ReadMultiReg) refers to mpu6050.o(i.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_GetFlagStatus) for I2C_GetFlagStatus
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_GenerateSTART) for I2C_GenerateSTART
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_CheckEvent) for I2C_CheckEvent
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_Send7bitAddress) for I2C_Send7bitAddress
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_SendData) for I2C_SendData
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_AcknowledgeConfig) for I2C_AcknowledgeConfig
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_GenerateSTOP) for I2C_GenerateSTOP
    mpu6050.o(i.MPU6050_ReadReg) refers to stm32f10x_i2c.o(i.I2C_ReceiveData) for I2C_ReceiveData
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_GetFlagStatus) for I2C_GetFlagStatus
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_GenerateSTART) for I2C_GenerateSTART
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_CheckEvent) for I2C_CheckEvent
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_Send7bitAddress) for I2C_Send7bitAddress
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_SendData) for I2C_SendData
    mpu6050.o(i.MPU6050_WriteReg) refers to stm32f10x_i2c.o(i.I2C_GenerateSTOP) for I2C_GenerateSTOP
    servo.o(i.Servo_AngleToPWM) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    servo.o(i.Servo_AngleToPWM) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    servo.o(i.Servo_AngleToPWM) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_AngleToPWM) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_AngleToPWM) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    servo.o(i.Servo_AngleToPWM) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    servo.o(i.Servo_Disable) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    servo.o(i.Servo_Enable) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    servo.o(i.Servo_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(i.Servo_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    servo.o(i.Servo_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    servo.o(i.Servo_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    servo.o(i.Servo_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    servo.o(i.Servo_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    servo.o(i.Servo_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    servo.o(i.Servo_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    servo.o(i.Servo_PWMToAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(i.Servo_PWMToAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_PWMToAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_SetAngle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    servo.o(i.Servo_SetAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    servo.o(i.Servo_SetAngle) refers to servo.o(i.Servo_AngleToPWM) for Servo_AngleToPWM
    servo.o(i.Servo_Update) refers to servo.o(i.Servo_PWMToAngle) for Servo_PWMToAngle
    servo.o(i.Servo_Update) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.__atan2f$lsc) for __atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f_x.o(i.__atan2f$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f_x.o(i.__atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.__atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing servo.o(i.Servo_Disable), (24 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (36 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (60 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (56 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (76 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (78 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (36 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (36 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).

50 unused section(s) (total 1434 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/fsqrt.s                         0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\misc.c    0x00000000   Number         0  misc.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c 0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    mpu6050.c                                0x00000000   Number         0  mpu6050.o ABSOLUTE
    servo.c                                  0x00000000   Number         0  servo.o ABSOLUTE
    startup_stm32f10x_hd.s                   0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800020c   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000270   Section        0  heapauxi.o(.text)
    .text                                    0x08000276   Section        0  _rserrno.o(.text)
    .text                                    0x0800028c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000294   Section        8  libspace.o(.text)
    .text                                    0x0800029c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080002e6   Section        0  exit.o(.text)
    .text                                    0x080002f8   Section        0  sys_exit.o(.text)
    .text                                    0x08000304   Section        2  use_no_semi.o(.text)
    .text                                    0x08000306   Section        0  indicate_semi.o(.text)
    i.Angle_Follow_Control                   0x08000308   Section        0  main.o(i.Angle_Follow_Control)
    Angle_Follow_Control                     0x08000309   Thumb Code   172  main.o(i.Angle_Follow_Control)
    i.Delay_ms                               0x080003e4   Section        0  main.o(i.Delay_ms)
    Delay_ms                                 0x080003e5   Thumb Code    32  main.o(i.Delay_ms)
    i.GPIO_Init                              0x08000404   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x0800051c   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000520   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.I2C_AcknowledgeConfig                  0x08000524   Section        0  stm32f10x_i2c.o(i.I2C_AcknowledgeConfig)
    i.I2C_CheckEvent                         0x0800053c   Section        0  stm32f10x_i2c.o(i.I2C_CheckEvent)
    i.I2C_Cmd                                0x08000566   Section        0  stm32f10x_i2c.o(i.I2C_Cmd)
    i.I2C_GenerateSTART                      0x0800057e   Section        0  stm32f10x_i2c.o(i.I2C_GenerateSTART)
    i.I2C_GenerateSTOP                       0x08000596   Section        0  stm32f10x_i2c.o(i.I2C_GenerateSTOP)
    i.I2C_GetFlagStatus                      0x080005ae   Section        0  stm32f10x_i2c.o(i.I2C_GetFlagStatus)
    i.I2C_Init                               0x080005e8   Section        0  stm32f10x_i2c.o(i.I2C_Init)
    i.I2C_ReceiveData                        0x080006c4   Section        0  stm32f10x_i2c.o(i.I2C_ReceiveData)
    i.I2C_Send7bitAddress                    0x080006cc   Section        0  stm32f10x_i2c.o(i.I2C_Send7bitAddress)
    i.I2C_SendData                           0x080006dc   Section        0  stm32f10x_i2c.o(i.I2C_SendData)
    i.LED_Toggle                             0x080006e0   Section        0  main.o(i.LED_Toggle)
    LED_Toggle                               0x080006e1   Thumb Code    48  main.o(i.LED_Toggle)
    i.MPU6050_CalcAngles                     0x08000718   Section        0  mpu6050.o(i.MPU6050_CalcAngles)
    i.MPU6050_Calibrate                      0x08000890   Section        0  mpu6050.o(i.MPU6050_Calibrate)
    i.MPU6050_I2C_Init                       0x08000910   Section        0  mpu6050.o(i.MPU6050_I2C_Init)
    MPU6050_I2C_Init                         0x08000911   Thumb Code   104  mpu6050.o(i.MPU6050_I2C_Init)
    i.MPU6050_Init                           0x08000984   Section        0  mpu6050.o(i.MPU6050_Init)
    i.MPU6050_ReadData                       0x080009ee   Section        0  mpu6050.o(i.MPU6050_ReadData)
    i.MPU6050_ReadMultiReg                   0x08000a68   Section        0  mpu6050.o(i.MPU6050_ReadMultiReg)
    i.MPU6050_ReadReg                        0x08000a94   Section        0  mpu6050.o(i.MPU6050_ReadReg)
    i.MPU6050_WriteReg                       0x08000bd0   Section        0  mpu6050.o(i.MPU6050_WriteReg)
    i.RCC_APB1PeriphClockCmd                 0x08000cac   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000ccc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000cec   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Servo_AngleToPWM                       0x08000dd0   Section        0  servo.o(i.Servo_AngleToPWM)
    i.Servo_Enable                           0x08000e20   Section        0  servo.o(i.Servo_Enable)
    i.Servo_Init                             0x08000e38   Section        0  servo.o(i.Servo_Init)
    i.Servo_PWMToAngle                       0x08000ed0   Section        0  servo.o(i.Servo_PWMToAngle)
    i.Servo_SetAngle                         0x08000f04   Section        0  servo.o(i.Servo_SetAngle)
    i.Servo_Update                           0x08000f34   Section        0  servo.o(i.Servo_Update)
    i.SystemInit                             0x08000f58   Section        0  system_stm32f10x.o(i.SystemInit)
    i.System_Init                            0x08000f68   Section        0  main.o(i.System_Init)
    System_Init                              0x08000f69   Thumb Code   182  main.o(i.System_Init)
    i.TIM_ARRPreloadConfig                   0x08001030   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_Cmd                                0x08001048   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_OC1Init                            0x08001060   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x080010a0   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_SetCompare1                        0x080010b2   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_TimeBaseInit                       0x080010b8   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART_Cmd                              0x08001100   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001118   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_Init                             0x0800112c   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_SendData                         0x080011f4   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.__ARM_fpclassifyf                      0x080011fc   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__mathlib_flt_infnan2                  0x08001222   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_underflow                0x08001226   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.atan2f                                 0x08001230   Section        0  atan2f.o(i.atan2f)
    i.main                                   0x0800148c   Section        0  main.o(i.main)
    i.sqrtf                                  0x0800151c   Section        0  sqrtf.o(i.sqrtf)
    x$fpl$fadd                               0x08001548   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08001557   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x0800160c   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$fcmpinf                            0x08001618   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x08001630   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08001631   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x080017b4   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x080017f4   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08001824   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x0800184c   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x080018b4   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080019b6   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08001a42   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08001a4c   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$frsb                               0x08001aae   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsqrt                              0x08001ac4   Section      272  fsqrt.o(x$fpl$fsqrt)
    x$fpl$fsub                               0x08001bd4   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08001be3   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$retnan                             0x08001cbe   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbnf                            0x08001d22   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$trapveneer                         0x08001d6e   Section       48  trapv.o(x$fpl$trapveneer)
    x$fpl$usenofp                            0x08001d9e   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       12  main.o(.data)
    led_state                                0x20000001   Data           1  main.o(.data)
    last_angle                               0x20000004   Data           4  main.o(.data)
    debug_counter                            0x20000008   Data           4  main.o(.data)
    .data                                    0x2000000c   Section       12  mpu6050.o(.data)
    last_time                                0x2000000c   Data           4  mpu6050.o(.data)
    angle_x                                  0x20000010   Data           4  mpu6050.o(.data)
    angle_y                                  0x20000014   Data           4  mpu6050.o(.data)
    .data                                    0x20000018   Section        4  system_stm32f10x.o(.data)
    .bss                                     0x2000001c   Section       48  main.o(.bss)
    .bss                                     0x2000004c   Section       96  libspace.o(.bss)
    HEAP                                     0x200000b0   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200000b0   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200002b0   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200002b0   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200006b0   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001cd   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x080001d5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x080001d7   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x080001d9   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x080001db   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x080001dd   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x080001df   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x080001e1   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x080001e3   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x080001e5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080001e9   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_memcpy4                          0x0800020d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800020d   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800020d   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000255   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x08000271   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000273   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000275   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x08000277   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000281   Thumb Code    12  _rserrno.o(.text)
    __aeabi_errno_addr                       0x0800028d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800028d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800028d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x08000295   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000295   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000295   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800029d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080002e7   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x080002f9   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000305   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000305   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000307   Thumb Code     0  indicate_semi.o(.text)
    GPIO_Init                                0x08000405   Thumb Code   280  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x0800051d   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000521   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    I2C_AcknowledgeConfig                    0x08000525   Thumb Code    24  stm32f10x_i2c.o(i.I2C_AcknowledgeConfig)
    I2C_CheckEvent                           0x0800053d   Thumb Code    42  stm32f10x_i2c.o(i.I2C_CheckEvent)
    I2C_Cmd                                  0x08000567   Thumb Code    24  stm32f10x_i2c.o(i.I2C_Cmd)
    I2C_GenerateSTART                        0x0800057f   Thumb Code    24  stm32f10x_i2c.o(i.I2C_GenerateSTART)
    I2C_GenerateSTOP                         0x08000597   Thumb Code    24  stm32f10x_i2c.o(i.I2C_GenerateSTOP)
    I2C_GetFlagStatus                        0x080005af   Thumb Code    58  stm32f10x_i2c.o(i.I2C_GetFlagStatus)
    I2C_Init                                 0x080005e9   Thumb Code   208  stm32f10x_i2c.o(i.I2C_Init)
    I2C_ReceiveData                          0x080006c5   Thumb Code     8  stm32f10x_i2c.o(i.I2C_ReceiveData)
    I2C_Send7bitAddress                      0x080006cd   Thumb Code    16  stm32f10x_i2c.o(i.I2C_Send7bitAddress)
    I2C_SendData                             0x080006dd   Thumb Code     4  stm32f10x_i2c.o(i.I2C_SendData)
    MPU6050_CalcAngles                       0x08000719   Thumb Code   340  mpu6050.o(i.MPU6050_CalcAngles)
    MPU6050_Calibrate                        0x08000891   Thumb Code   122  mpu6050.o(i.MPU6050_Calibrate)
    MPU6050_Init                             0x08000985   Thumb Code   106  mpu6050.o(i.MPU6050_Init)
    MPU6050_ReadData                         0x080009ef   Thumb Code   122  mpu6050.o(i.MPU6050_ReadData)
    MPU6050_ReadMultiReg                     0x08000a69   Thumb Code    44  mpu6050.o(i.MPU6050_ReadMultiReg)
    MPU6050_ReadReg                          0x08000a95   Thumb Code   304  mpu6050.o(i.MPU6050_ReadReg)
    MPU6050_WriteReg                         0x08000bd1   Thumb Code   208  mpu6050.o(i.MPU6050_WriteReg)
    RCC_APB1PeriphClockCmd                   0x08000cad   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000ccd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000ced   Thumb Code   216  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Servo_AngleToPWM                         0x08000dd1   Thumb Code    66  servo.o(i.Servo_AngleToPWM)
    Servo_Enable                             0x08000e21   Thumb Code    18  servo.o(i.Servo_Enable)
    Servo_Init                               0x08000e39   Thumb Code   142  servo.o(i.Servo_Init)
    Servo_PWMToAngle                         0x08000ed1   Thumb Code    42  servo.o(i.Servo_PWMToAngle)
    Servo_SetAngle                           0x08000f05   Thumb Code    42  servo.o(i.Servo_SetAngle)
    Servo_Update                             0x08000f35   Thumb Code    30  servo.o(i.Servo_Update)
    SystemInit                               0x08000f59   Thumb Code     8  system_stm32f10x.o(i.SystemInit)
    TIM_ARRPreloadConfig                     0x08001031   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_Cmd                                  0x08001049   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_OC1Init                              0x08001061   Thumb Code    64  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080010a1   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_SetCompare1                          0x080010b3   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_TimeBaseInit                         0x080010b9   Thumb Code    68  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART_Cmd                                0x08001101   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001119   Thumb Code    18  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_Init                               0x0800112d   Thumb Code   196  stm32f10x_usart.o(i.USART_Init)
    USART_SendData                           0x080011f5   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    __ARM_fpclassifyf                        0x080011fd   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __mathlib_flt_infnan2                    0x08001223   Thumb Code     4  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_underflow                  0x08001227   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    atan2f                                   0x08001231   Thumb Code   528  atan2f.o(i.atan2f)
    main                                     0x0800148d   Thumb Code   128  main.o(i.main)
    sqrtf                                    0x0800151d   Thumb Code    44  sqrtf.o(i.sqrtf)
    __aeabi_fadd                             0x08001549   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08001549   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x0800160d   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __fpl_fcmp_Inf                           0x08001619   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x08001631   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08001631   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x080017b5   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080017b5   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x080017f5   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080017f5   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08001825   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08001825   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x0800184d   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x0800184d   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x0800189f   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x080018b5   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080018b5   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080019b7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08001a43   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08001a4d   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08001a4d   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_frsub                            0x08001aaf   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08001aaf   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    _fsqrt                                   0x08001ac5   Thumb Code   272  fsqrt.o(x$fpl$fsqrt)
    __aeabi_fsub                             0x08001bd5   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08001bd5   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __fpl_return_NaN                         0x08001cbf   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbnf                            0x08001d23   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    __fpl_cmpreturn                          0x08001d6f   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08001d9e   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08001da0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001dc0   Number         0  anon$$obj.o(Region$$Table)
    system_status                            0x20000000   Data           1  main.o(.data)
    SystemCoreClock                          0x20000018   Data           4  system_stm32f10x.o(.data)
    mpu_data                                 0x2000001c   Data          36  main.o(.bss)
    servo                                    0x20000040   Data          12  main.o(.bss)
    __libspace_start                         0x2000004c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000ac   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001ddc, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001dc0, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          206    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          722  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          961    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          963    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          965    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO          828    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          834    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          836    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          839    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          841    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          843    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          846    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          848    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          850    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          852    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          854    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          856    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          858    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          860    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          862    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          864    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          866    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          870    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          872    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          874    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO          876    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO          877    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x080001a8   0x00000002   Code   RO          901    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          914    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          916    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          919    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          922    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          924    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO          927    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000002   Code   RO          928    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO          766    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO          795    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO          807    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO          797    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000004   Code   RO          798    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          800    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000008   Code   RO          801    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000002   Code   RO          831    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO          881    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO          882    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO          883    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000040   Code   RO          207    .text               startup_stm32f10x_hd.o
    0x0800020c   0x0800020c   0x00000064   Code   RO          718    .text               c_w.l(rt_memcpy_w.o)
    0x08000270   0x08000270   0x00000006   Code   RO          720    .text               c_w.l(heapauxi.o)
    0x08000276   0x08000276   0x00000016   Code   RO          767    .text               c_w.l(_rserrno.o)
    0x0800028c   0x0800028c   0x00000008   Code   RO          812    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000294   0x08000294   0x00000008   Code   RO          816    .text               c_w.l(libspace.o)
    0x0800029c   0x0800029c   0x0000004a   Code   RO          819    .text               c_w.l(sys_stackheap_outer.o)
    0x080002e6   0x080002e6   0x00000012   Code   RO          821    .text               c_w.l(exit.o)
    0x080002f8   0x080002f8   0x0000000c   Code   RO          893    .text               c_w.l(sys_exit.o)
    0x08000304   0x08000304   0x00000002   Code   RO          904    .text               c_w.l(use_no_semi.o)
    0x08000306   0x08000306   0x00000000   Code   RO          906    .text               c_w.l(indicate_semi.o)
    0x08000306   0x08000306   0x00000002   PAD
    0x08000308   0x08000308   0x000000dc   Code   RO            1    i.Angle_Follow_Control  main.o
    0x080003e4   0x080003e4   0x00000020   Code   RO            2    i.Delay_ms          main.o
    0x08000404   0x08000404   0x00000118   Code   RO          238    i.GPIO_Init         stm32f10x_gpio.o
    0x0800051c   0x0800051c   0x00000004   Code   RO          243    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000520   0x08000520   0x00000004   Code   RO          244    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000524   0x08000524   0x00000018   Code   RO          548    i.I2C_AcknowledgeConfig  stm32f10x_i2c.o
    0x0800053c   0x0800053c   0x0000002a   Code   RO          549    i.I2C_CheckEvent    stm32f10x_i2c.o
    0x08000566   0x08000566   0x00000018   Code   RO          550    i.I2C_Cmd           stm32f10x_i2c.o
    0x0800057e   0x0800057e   0x00000018   Code   RO          552    i.I2C_GenerateSTART  stm32f10x_i2c.o
    0x08000596   0x08000596   0x00000018   Code   RO          553    i.I2C_GenerateSTOP  stm32f10x_i2c.o
    0x080005ae   0x080005ae   0x0000003a   Code   RO          554    i.I2C_GetFlagStatus  stm32f10x_i2c.o
    0x080005e8   0x080005e8   0x000000dc   Code   RO          555    i.I2C_Init          stm32f10x_i2c.o
    0x080006c4   0x080006c4   0x00000008   Code   RO          556    i.I2C_ReceiveData   stm32f10x_i2c.o
    0x080006cc   0x080006cc   0x00000010   Code   RO          557    i.I2C_Send7bitAddress  stm32f10x_i2c.o
    0x080006dc   0x080006dc   0x00000004   Code   RO          558    i.I2C_SendData      stm32f10x_i2c.o
    0x080006e0   0x080006e0   0x00000038   Code   RO            3    i.LED_Toggle        main.o
    0x08000718   0x08000718   0x00000178   Code   RO           93    i.MPU6050_CalcAngles  mpu6050.o
    0x08000890   0x08000890   0x00000080   Code   RO           94    i.MPU6050_Calibrate  mpu6050.o
    0x08000910   0x08000910   0x00000074   Code   RO           95    i.MPU6050_I2C_Init  mpu6050.o
    0x08000984   0x08000984   0x0000006a   Code   RO           96    i.MPU6050_Init      mpu6050.o
    0x080009ee   0x080009ee   0x0000007a   Code   RO           97    i.MPU6050_ReadData  mpu6050.o
    0x08000a68   0x08000a68   0x0000002c   Code   RO           98    i.MPU6050_ReadMultiReg  mpu6050.o
    0x08000a94   0x08000a94   0x0000013c   Code   RO           99    i.MPU6050_ReadReg   mpu6050.o
    0x08000bd0   0x08000bd0   0x000000dc   Code   RO          100    i.MPU6050_WriteReg  mpu6050.o
    0x08000cac   0x08000cac   0x00000020   Code   RO          314    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000ccc   0x08000ccc   0x00000020   Code   RO          316    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000cec   0x08000cec   0x000000e4   Code   RO          319    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000dd0   0x08000dd0   0x00000050   Code   RO          156    i.Servo_AngleToPWM  servo.o
    0x08000e20   0x08000e20   0x00000018   Code   RO          158    i.Servo_Enable      servo.o
    0x08000e38   0x08000e38   0x00000098   Code   RO          159    i.Servo_Init        servo.o
    0x08000ed0   0x08000ed0   0x00000034   Code   RO          160    i.Servo_PWMToAngle  servo.o
    0x08000f04   0x08000f04   0x00000030   Code   RO          161    i.Servo_SetAngle    servo.o
    0x08000f34   0x08000f34   0x00000024   Code   RO          162    i.Servo_Update      servo.o
    0x08000f58   0x08000f58   0x00000010   Code   RO          213    i.SystemInit        system_stm32f10x.o
    0x08000f68   0x08000f68   0x000000c8   Code   RO            4    i.System_Init       main.o
    0x08001030   0x08001030   0x00000018   Code   RO          410    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08001048   0x08001048   0x00000018   Code   RO          411    i.TIM_Cmd           stm32f10x_tim.o
    0x08001060   0x08001060   0x00000040   Code   RO          415    i.TIM_OC1Init       stm32f10x_tim.o
    0x080010a0   0x080010a0   0x00000012   Code   RO          416    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x080010b2   0x080010b2   0x00000004   Code   RO          425    i.TIM_SetCompare1   stm32f10x_tim.o
    0x080010b6   0x080010b6   0x00000002   PAD
    0x080010b8   0x080010b8   0x00000048   Code   RO          430    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001100   0x08001100   0x00000018   Code   RO          627    i.USART_Cmd         stm32f10x_usart.o
    0x08001118   0x08001118   0x00000012   Code   RO          629    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x0800112a   0x0800112a   0x00000002   PAD
    0x0800112c   0x0800112c   0x000000c8   Code   RO          630    i.USART_Init        stm32f10x_usart.o
    0x080011f4   0x080011f4   0x00000008   Code   RO          632    i.USART_SendData    stm32f10x_usart.o
    0x080011fc   0x080011fc   0x00000026   Code   RO          778    i.__ARM_fpclassifyf  m_ws.l(fpclassifyf.o)
    0x08001222   0x08001222   0x00000004   Code   RO          782    i.__mathlib_flt_infnan2  m_ws.l(funder.o)
    0x08001226   0x08001226   0x0000000a   Code   RO          786    i.__mathlib_flt_underflow  m_ws.l(funder.o)
    0x08001230   0x08001230   0x0000025c   Code   RO          751    i.atan2f            m_ws.l(atan2f.o)
    0x0800148c   0x0800148c   0x00000090   Code   RO            5    i.main              main.o
    0x0800151c   0x0800151c   0x0000002c   Code   RO          759    i.sqrtf             m_ws.l(sqrtf.o)
    0x08001548   0x08001548   0x000000c4   Code   RO          724    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x0800160c   0x0800160c   0x0000000c   Code   RO          829    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x08001618   0x08001618   0x00000018   Code   RO          769    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08001630   0x08001630   0x00000184   Code   RO          731    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x080017b4   0x080017b4   0x0000003e   Code   RO          734    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x080017f2   0x080017f2   0x00000002   PAD
    0x080017f4   0x080017f4   0x00000030   Code   RO          739    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08001824   0x08001824   0x00000026   Code   RO          738    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x0800184a   0x0800184a   0x00000002   PAD
    0x0800184c   0x0800184c   0x00000068   Code   RO          744    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x080018b4   0x080018b4   0x00000102   Code   RO          746    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080019b6   0x080019b6   0x0000008c   Code   RO          771    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08001a42   0x08001a42   0x0000000a   Code   RO          773    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08001a4c   0x08001a4c   0x00000062   Code   RO          748    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08001aae   0x08001aae   0x00000014   Code   RO          725    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08001ac2   0x08001ac2   0x00000002   PAD
    0x08001ac4   0x08001ac4   0x00000110   Code   RO          775    x$fpl$fsqrt         fz_ws.l(fsqrt.o)
    0x08001bd4   0x08001bd4   0x000000ea   Code   RO          726    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08001cbe   0x08001cbe   0x00000064   Code   RO          878    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08001d22   0x08001d22   0x0000004c   Code   RO          814    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x08001d6e   0x08001d6e   0x00000030   Code   RO          891    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08001d9e   0x08001d9e   0x00000000   Code   RO          777    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08001d9e   0x08001d9e   0x00000002   PAD
    0x08001da0   0x08001da0   0x00000020   Data   RO          959    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001dc0, Size: 0x000006b0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001dc0   0x0000000c   Data   RW            7    .data               main.o
    0x2000000c   0x08001dcc   0x0000000c   Data   RW          101    .data               mpu6050.o
    0x20000018   0x08001dd8   0x00000004   Data   RW          214    .data               system_stm32f10x.o
    0x2000001c        -       0x00000030   Zero   RW            6    .bss                main.o
    0x2000004c        -       0x00000060   Zero   RW          817    .bss                c_w.l(libspace.o)
    0x200000ac   0x08001ddc   0x00000004   PAD
    0x200000b0        -       0x00000200   Zero   RW          205    HEAP                startup_stm32f10x_hd.o
    0x200002b0        -       0x00000400   Zero   RW          204    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       652         90          0         12         48      21768   main.o
      1428         78          0         12          0       5795   mpu6050.o
       392         52          0          0          0       3346   servo.o
        64         26        304          0       1536        796   startup_stm32f10x_hd.o
       288          0          0          0          0       2408   stm32f10x_gpio.o
       444         12          0          0          0       6384   stm32f10x_i2c.o
       292         24          0          0          0       2230   stm32f10x_rcc.o
       206          4          0          0          0       3843   stm32f10x_tim.o
       250          4          0          0          0       2888   stm32f10x_usart.o
        16          8          0          4          0        611   system_stm32f10x.o

    ----------------------------------------------------------------------
      4036        <USER>        <GROUP>         28       1584      50069   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       450          8          0          0          0        236   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
       272        100          0          0          0         88   fsqrt.o
       100          0          0          0          0         68   retnan.o
        76          0          0          0          0         68   scalbnf.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       604         76          0          0          0        116   atan2f.o
        38          0          0          0          0         68   fpclassifyf.o
        14          0          0          0          0        136   funder.o
        44          0          0          0          0         80   sqrtf.o

    ----------------------------------------------------------------------
      3244        <USER>          <GROUP>          0        100       2600   Library Totals
        14          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       402         20          0          0         96        832   c_w.l
      2128        204          0          0          0       1368   fz_ws.l
       700         76          0          0          0        400   m_ws.l

    ----------------------------------------------------------------------
      3244        <USER>          <GROUP>          0        100       2600   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7280        598        336         28       1684      48921   Grand Totals
      7280        598        336         28       1684      48921   ELF Image Totals
      7280        598        336         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7616 (   7.44kB)
    Total RW  Size (RW Data + ZI Data)              1712 (   1.67kB)
    Total ROM Size (Code + RO Data + RW Data)       7644 (   7.46kB)

==============================================================================

