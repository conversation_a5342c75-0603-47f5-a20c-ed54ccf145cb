#include "stm32f10x_gpio.h"
#include "stm32f10x_rcc.h"

// GPIO初始化
void GPIO_Init(GPIO_TypeDef* GPIOx, GPIO_InitTypeDef* GPIO_InitStruct) {
    uint32_t currentmode = 0x00, currentpin = 0x00, pinpos = 0x00, pos = 0x00;
    uint32_t tmpreg = 0x00, pinmask = 0x00;
    
    currentmode = ((uint32_t)GPIO_InitStruct->GPIO_Mode) & ((uint32_t)0x0F);
    if ((((uint32_t)GPIO_InitStruct->GPIO_Mode) & ((uint32_t)0x10)) != 0x00) {
        currentmode |= (uint32_t)GPIO_InitStruct->GPIO_Speed;
    }
    
    if (((uint32_t)GPIO_InitStruct->GPIO_Pin & ((uint32_t)0x00FF)) != 0x00) {
        tmpreg = GPIOx->CRL;
        for (pinpos = 0x00; pinpos < 0x08; pinpos++) {
            pos = ((uint32_t)0x01) << pinpos;
            currentpin = (GPIO_InitStruct->GPIO_Pin) & pos;
            if (currentpin == pos) {
                pos = pinpos << 2;
                pinmask = ((uint32_t)0x0F) << pos;
                tmpreg &= ~pinmask;
                tmpreg |= (currentmode << pos);
                if (GPIO_InitStruct->GPIO_Mode == GPIO_Mode_IPD) {
                    GPIOx->BRR = (((uint32_t)0x01) << pinpos);
                } else {
                    if (GPIO_InitStruct->GPIO_Mode == GPIO_Mode_IPU) {
                        GPIOx->BSRR = (((uint32_t)0x01) << pinpos);
                    }
                }
            }
        }
        GPIOx->CRL = tmpreg;
    }
    
    if (GPIO_InitStruct->GPIO_Pin > 0x00FF) {
        tmpreg = GPIOx->CRH;
        for (pinpos = 0x00; pinpos < 0x08; pinpos++) {
            pos = (((uint32_t)0x01) << (pinpos + 0x08));
            currentpin = ((GPIO_InitStruct->GPIO_Pin) & pos);
            if (currentpin == pos) {
                pos = pinpos << 2;
                pinmask = ((uint32_t)0x0F) << pos;
                tmpreg &= ~pinmask;
                tmpreg |= (currentmode << pos);
                if (GPIO_InitStruct->GPIO_Mode == GPIO_Mode_IPD) {
                    GPIOx->BRR = (((uint32_t)0x01) << (pinpos + 0x08));
                } else {
                    if (GPIO_InitStruct->GPIO_Mode == GPIO_Mode_IPU) {
                        GPIOx->BSRR = (((uint32_t)0x01) << (pinpos + 0x08));
                    }
                }
            }
        }
        GPIOx->CRH = tmpreg;
    }
}

// 设置GPIO引脚
void GPIO_SetBits(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin) {
    GPIOx->BSRR = GPIO_Pin;
}

// 复位GPIO引脚
void GPIO_ResetBits(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin) {
    GPIOx->BRR = GPIO_Pin;
}

// 读取GPIO输入数据位
uint8_t GPIO_ReadInputDataBit(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin) {
    uint8_t bitstatus = 0x00;
    
    if ((GPIOx->IDR & GPIO_Pin) != (uint32_t)Bit_RESET) {
        bitstatus = (uint8_t)Bit_SET;
    } else {
        bitstatus = (uint8_t)Bit_RESET;
    }
    return bitstatus;
}

// 读取GPIO输入数据
uint16_t GPIO_ReadInputData(GPIO_TypeDef* GPIOx) {
    return ((uint16_t)GPIOx->IDR);
}

// 读取GPIO输出数据位
uint8_t GPIO_ReadOutputDataBit(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin) {
    uint8_t bitstatus = 0x00;
    
    if ((GPIOx->ODR & GPIO_Pin) != (uint32_t)Bit_RESET) {
        bitstatus = (uint8_t)Bit_SET;
    } else {
        bitstatus = (uint8_t)Bit_RESET;
    }
    return bitstatus;
}

// 读取GPIO输出数据
uint16_t GPIO_ReadOutputData(GPIO_TypeDef* GPIOx) {
    return ((uint16_t)GPIOx->ODR);
}

// 写GPIO位
void GPIO_WriteBit(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin, BitAction BitVal) {
    if (BitVal != Bit_RESET) {
        GPIOx->BSRR = GPIO_Pin;
    } else {
        GPIOx->BRR = GPIO_Pin;
    }
}

// 写GPIO端口
void GPIO_Write(GPIO_TypeDef* GPIOx, uint16_t PortVal) {
    GPIOx->ODR = PortVal;
}

// GPIO结构体初始化
void GPIO_StructInit(GPIO_InitTypeDef* GPIO_InitStruct) {
    GPIO_InitStruct->GPIO_Pin  = GPIO_Pin_All;
    GPIO_InitStruct->GPIO_Speed = GPIO_Speed_2MHz;
    GPIO_InitStruct->GPIO_Mode = GPIO_Mode_IN_FLOATING;
}

// GPIO复位
void GPIO_DeInit(GPIO_TypeDef* GPIOx) {
    if (GPIOx == GPIOA) {
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOA, ENABLE);
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOA, DISABLE);
    } else if (GPIOx == GPIOB) {
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOB, ENABLE);
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOB, DISABLE);
    } else if (GPIOx == GPIOC) {
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOC, ENABLE);
        RCC_APB2PeriphResetCmd(RCC_APB2Periph_GPIOC, DISABLE);
    }
}

// AFIO复位
void GPIO_AFIODeInit(void) {
    RCC_APB2PeriphResetCmd(RCC_APB2Periph_AFIO, ENABLE);
    RCC_APB2PeriphResetCmd(RCC_APB2Periph_AFIO, DISABLE);
}
