# STM32F103RCT6 舵机跟随MPU6050角度控制系统

## 项目简介
基于STM32F103RCT6微控制器的舵机角度跟随控制系统，通过MPU6050六轴传感器检测角度变化，驱动舵机实时跟随转动。

## 功能特性
- 实时读取MPU6050的三轴加速度和陀螺仪数据
- 使用互补滤波算法计算精确角度
- 舵机PWM控制，角度范围0-180度
- 角度死区处理，避免抖动
- 串口调试输出，实时监控角度数据
- LED状态指示，显示系统工作状态

## 硬件连接

### STM32F103RCT6引脚分配
| 功能 | 引脚 | 说明 |
|------|------|------|
| MPU6050 SCL | PB6 | I2C时钟线 |
| MPU6050 SDA | PB7 | I2C数据线 |
| 舵机PWM | PA6 | TIM3_CH1输出 |
| 调试串口TX | PA9 | USART1发送 |
| 调试串口RX | PA10 | USART1接收 |
| 状态LED | PC13 | 板载LED |

### 外部连接
1. **MPU6050模块**
   - VCC → 3.3V
   - GND → GND
   - SCL → PB6
   - SDA → PB7

2. **舵机连接**
   - 红线(VCC) → 5V电源
   - 黑线(GND) → GND
   - 黄线(信号) → PA6

3. **调试串口** (可选)
   - USB转TTL模块
   - TX → PA10
   - RX → PA9
   - GND → GND

## 软件配置

### 关键参数 (config.h)
```c
#define SERVO_PWM_FREQ 50           // PWM频率50Hz
#define ANGLE_FILTER_ALPHA 0.98f    // 互补滤波系数
#define ANGLE_DEADZONE 2.0f         // 角度死区2度
#define CONTROL_LOOP_DELAY 10       // 控制循环10ms
```

### 编译环境
- **方式1**: Keil MDK-ARM 5.x
  - STM32F10x标准外设库
  - 目标芯片: STM32F103RCT6
  - 新建项目，添加所有.c和.h文件
  - 配置芯片型号和时钟

- **方式2**: GCC工具链 (推荐)
  - arm-none-eabi-gcc
  - 使用提供的Makefile编译
  - 命令: `make all`

## 使用方法

1. **硬件准备**
   - 按照连接图连接所有硬件
   - 确保电源供电正常(MPU6050用3.3V，舵机用5V)

2. **软件编译**
   - **Keil方式**: 在Keil中打开项目，选择目标芯片STM32F103RCT6，编译并下载
     - ✅ **编译状态**: 成功 (0错误，1警告)
     - 📦 **代码大小**: 7280字节
     - 📄 **输出文件**: servo_mpu6050.hex
   - **GCC方式**: 在项目目录执行 `make all` 编译，`make flash` 烧录

3. **系统启动**
   - 上电后LED快速闪烁表示正在校准MPU6050
   - 校准完成后LED慢速闪烁表示正常工作
   - 舵机初始位置为90度

4. **角度跟随**
   - 倾斜MPU6050模块，舵机会跟随X轴角度变化
   - 角度范围±90度映射到舵机0-180度
   - 小于2度的角度变化会被忽略(死区)

## 状态指示

### LED闪烁模式
- **快速闪烁(100ms)**: MPU6050初始化失败
- **中速闪烁(200ms)**: 舵机初始化失败  
- **慢速闪烁(工作中)**: 系统正常运行

### 串口调试输出
波特率115200，格式: `MPU:角度值 Servo:舵机角度`

## 技术特点

### 角度计算算法
- 使用互补滤波融合加速度计和陀螺仪数据
- 滤波系数0.98，平衡响应速度和稳定性
- 自动校准陀螺仪零偏

### PWM控制
- 50Hz标准舵机频率
- 脉宽范围0.5-2.5ms对应0-180度
- 使用TIM3定时器硬件PWM输出

### 性能优化
- 控制循环频率100Hz，响应迅速
- 角度死区避免微小抖动
- 高效的I2C通信和数据处理

## 故障排除

1. **舵机不动作**
   - 检查PWM信号线连接
   - 确认舵机电源供电
   - 验证角度变化是否超过死区

2. **角度读取异常**
   - 检查I2C连接线
   - 确认MPU6050供电正常
   - 重新校准传感器

3. **系统无响应**
   - 检查系统时钟配置
   - 确认程序正确下载
   - 观察LED状态指示

## 扩展功能
- 支持多轴角度控制
- 可调节跟随灵敏度
- 添加角度限位保护
- 支持多个舵机联动控制
