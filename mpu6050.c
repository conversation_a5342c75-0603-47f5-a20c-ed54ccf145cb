#include "stm32f10x.h"
#include "mpu6050.h"
#include <math.h>

static uint32_t last_time = 0; // 上次计算时间

// I2C初始化
static void MPU6050_I2C_Init(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef I2C_InitStructure;
    
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE); // 使能GPIOB时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_I2C1, ENABLE);  // 使能I2C1时钟
    
    GPIO_InitStructure.GPIO_Pin = MPU6050_SCL_PIN | MPU6050_SDA_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_OD; // 开漏复用输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(MPU6050_I2C_PORT, &GPIO_InitStructure);
    
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = 400000; // 400kHz
    I2C_Init(MPU6050_I2C, &I2C_InitStructure);
    I2C_Cmd(MPU6050_I2C, ENABLE);
}

// 写寄存器
uint8_t MPU6050_WriteReg(uint8_t reg, uint8_t data) {
    uint32_t timeout = MPU6050_TIMEOUT;
    
    while(I2C_GetFlagStatus(MPU6050_I2C, I2C_FLAG_BUSY) && timeout--); // 等待总线空闲
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_GenerateSTART(MPU6050_I2C, ENABLE); // 发送起始信号
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_MODE_SELECT) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_Send7bitAddress(MPU6050_I2C, MPU6050_ADDR << 1, I2C_Direction_Transmitter); // 发送设备地址
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_SendData(MPU6050_I2C, reg); // 发送寄存器地址
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_SendData(MPU6050_I2C, data); // 发送数据
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_GenerateSTOP(MPU6050_I2C, ENABLE); // 发送停止信号
    return ERROR_NONE;
}

// 读寄存器
uint8_t MPU6050_ReadReg(uint8_t reg, uint8_t *data) {
    uint32_t timeout = MPU6050_TIMEOUT;
    
    while(I2C_GetFlagStatus(MPU6050_I2C, I2C_FLAG_BUSY) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_GenerateSTART(MPU6050_I2C, ENABLE);
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_MODE_SELECT) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_Send7bitAddress(MPU6050_I2C, MPU6050_ADDR << 1, I2C_Direction_Transmitter);
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_SendData(MPU6050_I2C, reg);
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_GenerateSTART(MPU6050_I2C, ENABLE); // 重新起始
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_MODE_SELECT) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_Send7bitAddress(MPU6050_I2C, MPU6050_ADDR << 1, I2C_Direction_Receiver);
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_RECEIVER_MODE_SELECTED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    I2C_AcknowledgeConfig(MPU6050_I2C, DISABLE);
    I2C_GenerateSTOP(MPU6050_I2C, ENABLE);
    
    timeout = MPU6050_TIMEOUT;
    while(!I2C_CheckEvent(MPU6050_I2C, I2C_EVENT_MASTER_BYTE_RECEIVED) && timeout--);
    if(timeout == 0) return ERROR_I2C_TIMEOUT;
    
    *data = I2C_ReceiveData(MPU6050_I2C);
    I2C_AcknowledgeConfig(MPU6050_I2C, ENABLE);
    return ERROR_NONE;
}

// 读多个寄存器
uint8_t MPU6050_ReadMultiReg(uint8_t reg, uint8_t *data, uint8_t len) {
    for(uint8_t i = 0; i < len; i++) {
        if(MPU6050_ReadReg(reg + i, &data[i]) != ERROR_NONE) return ERROR_I2C_TIMEOUT;
    }
    return ERROR_NONE;
}

// 初始化MPU6050
uint8_t MPU6050_Init(void) {
    uint8_t temp;
    MPU6050_I2C_Init(); // 初始化I2C

    if(MPU6050_ReadReg(MPU6050_WHO_AM_I, &temp) != ERROR_NONE) return ERROR_MPU6050_INIT; // 检查设备ID
    if(temp != 0x68) return ERROR_MPU6050_INIT;

    if(MPU6050_WriteReg(MPU6050_PWR_MGMT_1, 0x00) != ERROR_NONE) return ERROR_MPU6050_INIT; // 唤醒设备
    if(MPU6050_WriteReg(MPU6050_SMPLRT_DIV, 0x07) != ERROR_NONE) return ERROR_MPU6050_INIT; // 设置采样率1kHz
    if(MPU6050_WriteReg(MPU6050_CONFIG, 0x06) != ERROR_NONE) return ERROR_MPU6050_INIT; // 设置低通滤波
    if(MPU6050_WriteReg(MPU6050_GYRO_CONFIG, 0x18) != ERROR_NONE) return ERROR_MPU6050_INIT; // 陀螺仪±2000°/s
    if(MPU6050_WriteReg(MPU6050_ACCEL_CONFIG, 0x01) != ERROR_NONE) return ERROR_MPU6050_INIT; // 加速度计±2g

    return ERROR_NONE;
}

// 读取传感器数据
uint8_t MPU6050_ReadData(MPU6050_Data *data) {
    uint8_t buf[14];
    if(MPU6050_ReadMultiReg(MPU6050_ACCEL_XOUT_H, buf, 14) != ERROR_NONE) return ERROR_I2C_TIMEOUT;

    data->accel_x = (int16_t)((buf[0] << 8) | buf[1]);
    data->accel_y = (int16_t)((buf[2] << 8) | buf[3]);
    data->accel_z = (int16_t)((buf[4] << 8) | buf[5]);
    data->gyro_x = (int16_t)((buf[8] << 8) | buf[9]);
    data->gyro_y = (int16_t)((buf[10] << 8) | buf[11]);
    data->gyro_z = (int16_t)((buf[12] << 8) | buf[13]);

    return ERROR_NONE;
}

// 校准陀螺仪
void MPU6050_Calibrate(MPU6050_Data *data) {
    int32_t sum_x = 0, sum_y = 0, sum_z = 0;
    for(int i = 0; i < 1000; i++) { // 采集1000次数据求平均
        MPU6050_ReadData(data);
        sum_x += data->gyro_x;
        sum_y += data->gyro_y;
        sum_z += data->gyro_z;
        for(volatile int j = 0; j < 1000; j++); // 简单延时
    }
    data->gyro_offset_x = sum_x / 1000.0f;
    data->gyro_offset_y = sum_y / 1000.0f;
    data->gyro_offset_z = sum_z / 1000.0f;
}

// 计算角度(互补滤波)
void MPU6050_CalcAngles(MPU6050_Data *data) {
    static float angle_x = 0, angle_y = 0;
    uint32_t current_time = SysTick->VAL; // 使用SysTick计时
    float dt = (last_time - current_time) / (float)SYSTEM_CLOCK_FREQ; // 计算时间差
    if(dt < 0) dt += 0xFFFFFF / (float)SYSTEM_CLOCK_FREQ;
    last_time = current_time;

    // 加速度计角度
    float accel_angle_x = atan2f(data->accel_y, data->accel_z) * RAD_TO_DEG;
    float accel_angle_y = atan2f(-data->accel_x, sqrtf(data->accel_y * data->accel_y + data->accel_z * data->accel_z)) * RAD_TO_DEG;

    // 陀螺仪角速度(去除零偏)
    float gyro_rate_x = (data->gyro_x - data->gyro_offset_x) / 131.0f; // ±2000°/s量程
    float gyro_rate_y = (data->gyro_y - data->gyro_offset_y) / 131.0f;

    // 互补滤波
    angle_x = ANGLE_FILTER_ALPHA * (angle_x + gyro_rate_x * dt) + (1 - ANGLE_FILTER_ALPHA) * accel_angle_x;
    angle_y = ANGLE_FILTER_ALPHA * (angle_y + gyro_rate_y * dt) + (1 - ANGLE_FILTER_ALPHA) * accel_angle_y;

    data->angle_x = angle_x;
    data->angle_y = angle_y;
    data->angle_z = (data->gyro_z - data->gyro_offset_z) / 131.0f; // Z轴角速度
}
