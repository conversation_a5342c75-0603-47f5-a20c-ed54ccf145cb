# STM32F103RCT6 舵机跟随MPU6050项目 Makefile

# 项目名称
PROJECT = servo_mpu6050_follow

# 目标芯片
MCU = STM32F103RCT6

# 编译器设置
CC = arm-none-eabi-gcc
AS = arm-none-eabi-as
LD = arm-none-eabi-ld
OBJCOPY = arm-none-eabi-objcopy
SIZE = arm-none-eabi-size

# 编译选项
CFLAGS = -mcpu=cortex-m3 -mthumb -Wall -O2 -g
CFLAGS += -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER
CFLAGS += -I. -Istm32f10x_lib/inc

# 链接选项
LDFLAGS = -mcpu=cortex-m3 -mthumb -nostartfiles
LDFLAGS += -Tstm32f103rct6.ld

# 源文件
SOURCES = main.c mpu6050.c servo.c
SOURCES += startup_stm32f10x_hd.s

# 目标文件
OBJECTS = $(SOURCES:.c=.o)
OBJECTS := $(OBJECTS:.s=.o)

# 默认目标
all: $(PROJECT).hex

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

%.o: %.s
	$(AS) -mcpu=cortex-m3 -mthumb $< -o $@

# 链接
$(PROJECT).elf: $(OBJECTS)
	$(CC) $(LDFLAGS) $(OBJECTS) -o $@
	$(SIZE) $@

# 生成hex文件
$(PROJECT).hex: $(PROJECT).elf
	$(OBJCOPY) -O ihex $< $@

# 清理
clean:
	rm -f *.o *.elf *.hex

# 烧录(需要ST-Link)
flash: $(PROJECT).hex
	st-flash --format ihex write $(PROJECT).hex

.PHONY: all clean flash
