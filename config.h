#ifndef CONFIG_H
#define CONFIG_H

#include "stm32f10x.h"

// 系统时钟配置
#define SYSTEM_CLOCK_FREQ 72000000  // 72MHz系统时钟

// MPU6050 I2C配置
#define MPU6050_I2C_PORT GPIOB      // I2C引脚端口
#define MPU6050_SCL_PIN GPIO_Pin_6  // I2C时钟引脚PB6
#define MPU6050_SDA_PIN GPIO_Pin_7  // I2C数据引脚PB7
#define MPU6050_I2C I2C1            // 使用I2C1
#define MPU6050_ADDR 0x68           // MPU6050设备地址
#define MPU6050_TIMEOUT 1000        // I2C超时时间(ms)

// 舵机PWM配置
#define SERVO_PWM_PORT GPIOA        // PWM输出端口
#define SERVO_PWM_PIN GPIO_Pin_0    // PWM输出引脚PA0
#define SERVO_TIMER TIM2            // 使用定时器TIM2
#define SERVO_PWM_CHANNEL TIM_Channel_1 // 使用通道1
#define SERVO_PWM_FREQ 50           // PWM频率50Hz
#define SERVO_PWM_PERIOD 20000      // PWM周期20ms(微秒)
#define SERVO_PWM_MIN 500           // 最小脉宽0.5ms(微秒)
#define SERVO_PWM_MAX 2500          // 最大脉宽2.5ms(微秒)
#define SERVO_ANGLE_MIN 0           // 舵机最小角度
#define SERVO_ANGLE_MAX 180         // 舵机最大角度

// 角度跟随控制参数
#define ANGLE_FILTER_ALPHA 0.98f    // 互补滤波系数
#define ANGLE_DEADZONE 2.0f         // 角度死区(度)
#define ANGLE_SCALE_FACTOR 1.0f     // 角度缩放因子
#define CONTROL_LOOP_FREQ 100       // 控制循环频率(Hz)
#define CONTROL_LOOP_DELAY 10       // 控制循环延时(ms)

// 调试配置
#define DEBUG_UART_ENABLE 1         // 启用串口调试
#define DEBUG_UART_PORT GPIOA       // 串口端口
#define DEBUG_UART_TX_PIN GPIO_Pin_9 // 串口TX引脚PA9
#define DEBUG_UART_RX_PIN GPIO_Pin_10 // 串口RX引脚PA10
#define DEBUG_UART_BAUDRATE 115200  // 串口波特率

// 系统状态LED配置
#define STATUS_LED_PORT GPIOC       // 状态LED端口
#define STATUS_LED_PIN GPIO_Pin_13  // 状态LED引脚PC13

// 数学常量
#define PI 3.14159265359f           // 圆周率
#define RAD_TO_DEG 57.2957795131f   // 弧度转角度
#define DEG_TO_RAD 0.0174532925f    // 角度转弧度

// 系统错误代码
#define ERROR_NONE 0                // 无错误
#define ERROR_MPU6050_INIT 1        // MPU6050初始化失败
#define ERROR_I2C_TIMEOUT 2         // I2C通信超时
#define ERROR_SERVO_INIT 3          // 舵机初始化失败
#define ERROR_INVALID_ANGLE 4       // 无效角度值

#endif // CONFIG_H
