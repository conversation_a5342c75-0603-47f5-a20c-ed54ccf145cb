#include "stm32f10x.h"

// 系统时钟频率变量
uint32_t SystemCoreClock = 72000000; // 72MHz

// 系统初始化函数
void SystemInit(void) {
    // 复位RCC时钟配置为默认复位状态
    RCC->CR |= (uint32_t)0x00000001;         // 设置HSION位
    RCC->CFGR &= (uint32_t)0xF8FF0000;       // 复位SW, HPRE, PPRE1, PPRE2, ADCPRE和MCO位
    RCC->CR &= (uint32_t)0xFEF6FFFF;         // 复位HSEON, CSSON和PLLON位
    RCC->CR &= (uint32_t)0xFFFBFFFF;         // 复位HSEBYP位
    RCC->CFGR &= (uint32_t)0xFF80FFFF;       // 复位PLLSRC, PLLXTPRE, PLLMUL和USBPRE/OTGFSPRE位
    RCC->CIR = 0x009F0000;                   // 禁用所有中断并清除挂起位
    
    // 配置系统时钟为72MHz
    // 启用HSE
    RCC->CR |= ((uint32_t)RCC_CR_HSEON);
    while(!(RCC->CR & RCC_CR_HSERDY));       // 等待HSE就绪
    
    // 配置Flash预取缓冲区和等待状态
    FLASH->ACR |= FLASH_ACR_PRFTBE;          // 使能预取缓冲区
    FLASH->ACR &= (uint32_t)((uint32_t)~FLASH_ACR_LATENCY);
    FLASH->ACR |= (uint32_t)FLASH_ACR_LATENCY_2; // Flash 2个等待状态
    
    // 配置HCLK, PCLK2和PCLK1预分频器
    RCC->CFGR |= (uint32_t)RCC_CFGR_HPRE_DIV1;   // HCLK = SYSCLK
    RCC->CFGR |= (uint32_t)RCC_CFGR_PPRE2_DIV1;  // PCLK2 = HCLK
    RCC->CFGR |= (uint32_t)RCC_CFGR_PPRE1_DIV2;  // PCLK1 = HCLK/2
    
    // 配置PLL
    RCC->CFGR &= (uint32_t)((uint32_t)~(RCC_CFGR_PLLSRC | RCC_CFGR_PLLXTPRE | RCC_CFGR_PLLMULL));
    RCC->CFGR |= (uint32_t)(RCC_CFGR_PLLSRC_HSE | RCC_CFGR_PLLMULL9); // PLL = HSE * 9 = 72MHz
    
    // 启用PLL
    RCC->CR |= RCC_CR_PLLON;
    while((RCC->CR & RCC_CR_PLLRDY) == 0);   // 等待PLL就绪
    
    // 选择PLL作为系统时钟源
    RCC->CFGR &= (uint32_t)((uint32_t)~(RCC_CFGR_SW));
    RCC->CFGR |= (uint32_t)RCC_CFGR_SW_PLL;
    while ((RCC->CFGR & (uint32_t)RCC_CFGR_SWS) != (uint32_t)0x08); // 等待PLL被选为系统时钟源
}

// 更新系统时钟频率
void SystemCoreClockUpdate(void) {
    uint32_t tmp = 0, pllmull = 0, pllsource = 0;
    
    // 获取SYSCLK源
    tmp = RCC->CFGR & RCC_CFGR_SWS;
    
    switch (tmp) {
        case 0x00:  // HSI作为系统时钟
            SystemCoreClock = 8000000; // HSI = 8MHz
            break;
        case 0x04:  // HSE作为系统时钟
            SystemCoreClock = 8000000; // HSE = 8MHz
            break;
        case 0x08:  // PLL作为系统时钟
            // 获取PLL时钟源和倍频因子
            pllmull = RCC->CFGR & RCC_CFGR_PLLMULL;
            pllsource = RCC->CFGR & RCC_CFGR_PLLSRC;
            
            pllmull = ( pllmull >> 18) + 2;
            
            if (pllsource == 0x00) {
                // HSI振荡器时钟除以2选为PLL时钟输入
                SystemCoreClock = (8000000 >> 1) * pllmull;
            } else {
                // HSE选为PLL时钟输入
                if ((RCC->CFGR & RCC_CFGR_PLLXTPRE) != (uint32_t)RESET) {
                    // HSE振荡器时钟除以2
                    SystemCoreClock = (8000000 >> 1) * pllmull;
                } else {
                    SystemCoreClock = 8000000 * pllmull;
                }
            }
            break;
        default:
            SystemCoreClock = 8000000;
            break;
    }
    
    // 计算HCLK频率
    tmp = RCC->CFGR & RCC_CFGR_HPRE;
    tmp = tmp >> 4;
    if (tmp & 0x08) {
        SystemCoreClock >>= ((tmp & 0x07) + 1);
    }
}

// 使用标准库中的定义
