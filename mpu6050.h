#ifndef MPU6050_H
#define MPU6050_H

#include "config.h"

// MPU6050寄存器地址
#define MPU6050_PWR_MGMT_1 0x6B     // 电源管理寄存器1
#define MPU6050_SMPLRT_DIV 0x19     // 采样率分频器
#define MPU6050_CONFIG 0x1A         // 配置寄存器
#define MPU6050_GYRO_CONFIG 0x1B    // 陀螺仪配置
#define MPU6050_ACCEL_CONFIG 0x1C   // 加速度计配置
#define MPU6050_ACCEL_XOUT_H 0x3B   // 加速度X轴高字节
#define MPU6050_GYRO_XOUT_H 0x43    // 陀螺仪X轴高字节
#define MPU6050_WHO_AM_I 0x75       // 设备ID寄存器

// 数据结构
typedef struct {
    int16_t accel_x, accel_y, accel_z;  // 加速度原始数据
    int16_t gyro_x, gyro_y, gyro_z;     // 陀螺仪原始数据
    float angle_x, angle_y, angle_z;    // 计算得到的角度
    float gyro_offset_x, gyro_offset_y, gyro_offset_z; // 陀螺仪零偏
} MPU6050_Data;

// 函数声明
uint8_t MPU6050_Init(void);            // 初始化MPU6050
uint8_t MPU6050_ReadData(MPU6050_Data *data); // 读取传感器数据
void MPU6050_CalcAngles(MPU6050_Data *data);  // 计算角度
void MPU6050_Calibrate(MPU6050_Data *data);   // 校准陀螺仪
uint8_t MPU6050_WriteReg(uint8_t reg, uint8_t data); // 写寄存器
uint8_t MPU6050_ReadReg(uint8_t reg, uint8_t *data); // 读寄存器
uint8_t MPU6050_ReadMultiReg(uint8_t reg, uint8_t *data, uint8_t len); // 读多个寄存器

#endif // MPU6050_H
