#include "stm32f10x_tim.h"
#include "stm32f10x_rcc.h"

// TIM时基初始化
void TIM_TimeBaseInit(TIM_TypeDef* TIMx, TIM_TimeBaseInitTypeDef* TIM_TimeBaseInitStruct) {
    uint16_t tmpcr1 = 0;
    
    tmpcr1 = TIMx->CR1;
    
    if((TIMx == TIM2) || (TIMx == TIM3)) {
        tmpcr1 &= (uint16_t)(~(((uint16_t)0x0010) | ((uint16_t)0x0060) | ((uint16_t)0x0300)));
        tmpcr1 |= (uint32_t)TIM_TimeBaseInitStruct->TIM_CounterMode;
    }
    
    tmpcr1 &= (uint16_t)(~((uint16_t)0x0300));
    tmpcr1 |= (uint32_t)TIM_TimeBaseInitStruct->TIM_ClockDivision;
    
    TIMx->CR1 = tmpcr1;
    TIMx->ARR = TIM_TimeBaseInitStruct->TIM_Period;
    TIMx->PSC = TIM_TimeBaseInitStruct->TIM_Prescaler;
    
    if ((TIMx == TIM2) || (TIMx == TIM3)) {
        TIMx->RCR = TIM_TimeBaseInitStruct->TIM_RepetitionCounter;
    }
    
    TIMx->EGR = ((uint16_t)0x0001); // 产生更新事件
}

// TIM输出比较1初始化
void TIM_OC1Init(TIM_TypeDef* TIMx, TIM_OCInitTypeDef* TIM_OCInitStruct) {
    uint16_t tmpccmrx = 0, tmpccer = 0, tmpcr2 = 0;
    
    TIMx->CCER &= (uint16_t)(~(uint16_t)0x0001); // 禁用通道
    
    tmpccer = TIMx->CCER;
    tmpcr2 = TIMx->CR2;
    
    tmpccmrx = TIMx->CCMR1;
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0003));
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0070));
    tmpccmrx |= TIM_OCInitStruct->TIM_OCMode;
    
    TIMx->CCMR1 = tmpccmrx;
    
    TIMx->CCR1 = TIM_OCInitStruct->TIM_Pulse;
    
    tmpccer &= (uint16_t)(~((uint16_t)0x0001));
    tmpccer &= (uint16_t)(~((uint16_t)0x0002));
    
    tmpccer |= TIM_OCInitStruct->TIM_OutputState;
    tmpccer |= TIM_OCInitStruct->TIM_OCPolarity;
    
    TIMx->CCER = tmpccer;
}

// 使能或禁用TIM
void TIM_Cmd(TIM_TypeDef* TIMx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        TIMx->CR1 |= ((uint16_t)0x0001);
    } else {
        TIMx->CR1 &= (uint16_t)(~((uint16_t)0x0001));
    }
}

// 配置TIM输出比较1预装载
void TIM_OC1PreloadConfig(TIM_TypeDef* TIMx, uint16_t TIM_OCPreload) {
    uint16_t tmpccmr1 = 0;
    tmpccmr1 = TIMx->CCMR1;
    tmpccmr1 &= (uint16_t)(~((uint16_t)0x0008));
    tmpccmr1 |= TIM_OCPreload;
    TIMx->CCMR1 = tmpccmr1;
}

// 配置TIM自动重装载预装载
void TIM_ARRPreloadConfig(TIM_TypeDef* TIMx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        TIMx->CR1 |= ((uint16_t)0x0080);
    } else {
        TIMx->CR1 &= (uint16_t)(~((uint16_t)0x0080));
    }
}

// 设置TIM比较寄存器1值
void TIM_SetCompare1(TIM_TypeDef* TIMx, uint16_t Compare1) {
    TIMx->CCR1 = Compare1;
}

// 设置TIM比较寄存器2值
void TIM_SetCompare2(TIM_TypeDef* TIMx, uint16_t Compare2) {
    TIMx->CCR2 = Compare2;
}

// 设置TIM比较寄存器3值
void TIM_SetCompare3(TIM_TypeDef* TIMx, uint16_t Compare3) {
    TIMx->CCR3 = Compare3;
}

// 设置TIM比较寄存器4值
void TIM_SetCompare4(TIM_TypeDef* TIMx, uint16_t Compare4) {
    TIMx->CCR4 = Compare4;
}

// 设置TIM计数器值
void TIM_SetCounter(TIM_TypeDef* TIMx, uint16_t Counter) {
    TIMx->CNT = Counter;
}

// 设置TIM自动重装载值
void TIM_SetAutoreload(TIM_TypeDef* TIMx, uint16_t Autoreload) {
    TIMx->ARR = Autoreload;
}

// 获取TIM计数器值
uint16_t TIM_GetCounter(TIM_TypeDef* TIMx) {
    return TIMx->CNT;
}

// 获取TIM预分频器值
uint16_t TIM_GetPrescaler(TIM_TypeDef* TIMx) {
    return TIMx->PSC;
}

// TIM结构体初始化
void TIM_TimeBaseStructInit(TIM_TimeBaseInitTypeDef* TIM_TimeBaseInitStruct) {
    TIM_TimeBaseInitStruct->TIM_Period = 0xFFFF;
    TIM_TimeBaseInitStruct->TIM_Prescaler = 0x0000;
    TIM_TimeBaseInitStruct->TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInitStruct->TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInitStruct->TIM_RepetitionCounter = 0x0000;
}

// TIM输出比较结构体初始化
void TIM_OCStructInit(TIM_OCInitTypeDef* TIM_OCInitStruct) {
    TIM_OCInitStruct->TIM_OCMode = TIM_OCMode_Timing;
    TIM_OCInitStruct->TIM_OutputState = TIM_OutputState_Disable;
    TIM_OCInitStruct->TIM_OutputNState = TIM_OutputState_Disable;
    TIM_OCInitStruct->TIM_Pulse = 0x0000;
    TIM_OCInitStruct->TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OCInitStruct->TIM_OCNPolarity = TIM_OCPolarity_High;
    TIM_OCInitStruct->TIM_OCIdleState = TIM_OutputState_Disable;
    TIM_OCInitStruct->TIM_OCNIdleState = TIM_OutputState_Disable;
}

// TIM复位
void TIM_DeInit(TIM_TypeDef* TIMx) {
    if (TIMx == TIM2) {
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_TIM2, ENABLE);
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_TIM2, DISABLE);
    } else if (TIMx == TIM3) {
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_TIM3, ENABLE);
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_TIM3, DISABLE);
    }
}

// TIM输出比较2初始化
void TIM_OC2Init(TIM_TypeDef* TIMx, TIM_OCInitTypeDef* TIM_OCInitStruct) {
    uint16_t tmpccmrx = 0, tmpccer = 0;
    
    TIMx->CCER &= (uint16_t)(~(uint16_t)0x0010);
    
    tmpccer = TIMx->CCER;
    tmpccmrx = TIMx->CCMR1;
    
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0300));
    tmpccmrx &= (uint16_t)(~((uint16_t)0x7000));
    tmpccmrx |= (uint16_t)(TIM_OCInitStruct->TIM_OCMode << 8);
    
    TIMx->CCMR1 = tmpccmrx;
    TIMx->CCR2 = TIM_OCInitStruct->TIM_Pulse;
    
    tmpccer &= (uint16_t)(~((uint16_t)0x0010));
    tmpccer &= (uint16_t)(~((uint16_t)0x0020));
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OutputState << 4);
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OCPolarity << 4);
    
    TIMx->CCER = tmpccer;
}

// TIM输出比较3初始化
void TIM_OC3Init(TIM_TypeDef* TIMx, TIM_OCInitTypeDef* TIM_OCInitStruct) {
    uint16_t tmpccmrx = 0, tmpccer = 0;
    
    TIMx->CCER &= (uint16_t)(~(uint16_t)0x0100);
    
    tmpccer = TIMx->CCER;
    tmpccmrx = TIMx->CCMR2;
    
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0003));
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0070));
    tmpccmrx |= TIM_OCInitStruct->TIM_OCMode;
    
    TIMx->CCMR2 = tmpccmrx;
    TIMx->CCR3 = TIM_OCInitStruct->TIM_Pulse;
    
    tmpccer &= (uint16_t)(~((uint16_t)0x0100));
    tmpccer &= (uint16_t)(~((uint16_t)0x0200));
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OutputState << 8);
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OCPolarity << 8);
    
    TIMx->CCER = tmpccer;
}

// TIM输出比较4初始化
void TIM_OC4Init(TIM_TypeDef* TIMx, TIM_OCInitTypeDef* TIM_OCInitStruct) {
    uint16_t tmpccmrx = 0, tmpccer = 0;
    
    TIMx->CCER &= (uint16_t)(~(uint16_t)0x1000);
    
    tmpccer = TIMx->CCER;
    tmpccmrx = TIMx->CCMR2;
    
    tmpccmrx &= (uint16_t)(~((uint16_t)0x0300));
    tmpccmrx &= (uint16_t)(~((uint16_t)0x7000));
    tmpccmrx |= (uint16_t)(TIM_OCInitStruct->TIM_OCMode << 8);
    
    TIMx->CCMR2 = tmpccmrx;
    TIMx->CCR4 = TIM_OCInitStruct->TIM_Pulse;
    
    tmpccer &= (uint16_t)(~((uint16_t)0x1000));
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OutputState << 12);
    tmpccer |= (uint16_t)(TIM_OCInitStruct->TIM_OCPolarity << 12);
    
    TIMx->CCER = tmpccer;
}

// 配置TIM输出比较2预装载
void TIM_OC2PreloadConfig(TIM_TypeDef* TIMx, uint16_t TIM_OCPreload) {
    uint16_t tmpccmr1 = 0;
    tmpccmr1 = TIMx->CCMR1;
    tmpccmr1 &= (uint16_t)(~((uint16_t)0x0800));
    tmpccmr1 |= (uint16_t)(TIM_OCPreload << 8);
    TIMx->CCMR1 = tmpccmr1;
}

// 配置TIM输出比较3预装载
void TIM_OC3PreloadConfig(TIM_TypeDef* TIMx, uint16_t TIM_OCPreload) {
    uint16_t tmpccmr2 = 0;
    tmpccmr2 = TIMx->CCMR2;
    tmpccmr2 &= (uint16_t)(~((uint16_t)0x0008));
    tmpccmr2 |= TIM_OCPreload;
    TIMx->CCMR2 = tmpccmr2;
}

// 配置TIM输出比较4预装载
void TIM_OC4PreloadConfig(TIM_TypeDef* TIMx, uint16_t TIM_OCPreload) {
    uint16_t tmpccmr2 = 0;
    tmpccmr2 = TIMx->CCMR2;
    tmpccmr2 &= (uint16_t)(~((uint16_t)0x0800));
    tmpccmr2 |= (uint16_t)(TIM_OCPreload << 8);
    TIMx->CCMR2 = tmpccmr2;
}
