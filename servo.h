#ifndef SERVO_H
#define SERVO_H

#include "config.h"

// 舵机控制结构体
typedef struct {
    float current_angle;    // 当前角度
    float target_angle;     // 目标角度
    uint16_t pwm_value;     // PWM占空比值
    uint8_t is_enabled;     // 使能状态
} Servo_Control;

// 函数声明
uint8_t Servo_Init(void);                          // 初始化舵机PWM
void Servo_SetAngle(Servo_Control *servo, float angle); // 设置舵机角度
void Servo_Update(Servo_Control *servo);           // 更新舵机PWM输出
void Servo_Enable(Servo_Control *servo);           // 使能舵机
void Servo_Disable(Servo_Control *servo);          // 禁用舵机
uint16_t Servo_AngleToPWM(float angle);            // 角度转PWM值
float Servo_PWMToAngle(uint16_t pwm);               // PWM值转角度

#endif // SERVO_H
