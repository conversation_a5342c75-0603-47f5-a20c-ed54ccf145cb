#include "stm32f10x.h"
#include "servo.h"

// 初始化舵机PWM
uint8_t Servo_Init(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);
    
    // 配置GPIO
    GPIO_InitStructure.GPIO_Pin = SERVO_PWM_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP; // 复用推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(SERVO_PWM_PORT, &GPIO_InitStructure);
    
    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Period = SERVO_PWM_PERIOD - 1; // 自动重装载值
    TIM_TimeBaseStructure.TIM_Prescaler = 999; // 预分频器(72MHz/1000=72kHz)
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(SERVO_TIMER, &TIM_TimeBaseStructure);
    
    // 配置PWM输出
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = SERVO_PWM_MIN; // 初始脉宽
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OC1Init(SERVO_TIMER, &TIM_OCInitStructure);
    TIM_OC1PreloadConfig(SERVO_TIMER, TIM_OCPreload_Enable);
    
    TIM_ARRPreloadConfig(SERVO_TIMER, ENABLE);
    TIM_Cmd(SERVO_TIMER, ENABLE); // 启动定时器
    
    return ERROR_NONE;
}

// 角度转PWM值
uint16_t Servo_AngleToPWM(float angle) {
    if(angle < SERVO_ANGLE_MIN) angle = SERVO_ANGLE_MIN; // 限制角度范围
    if(angle > SERVO_ANGLE_MAX) angle = SERVO_ANGLE_MAX;
    
    // 线性映射: 0°->36, 180°->180
    return (uint16_t)(SERVO_PWM_MIN + (angle / SERVO_ANGLE_MAX) * (SERVO_PWM_MAX - SERVO_PWM_MIN));
}

// PWM值转角度
float Servo_PWMToAngle(uint16_t pwm) {
    if(pwm < SERVO_PWM_MIN) pwm = SERVO_PWM_MIN;
    if(pwm > SERVO_PWM_MAX) pwm = SERVO_PWM_MAX;
    
    return (float)(pwm - SERVO_PWM_MIN) * SERVO_ANGLE_MAX / (SERVO_PWM_MAX - SERVO_PWM_MIN);
}

// 设置舵机角度
void Servo_SetAngle(Servo_Control *servo, float angle) {
    if(angle < SERVO_ANGLE_MIN || angle > SERVO_ANGLE_MAX) return; // 角度范围检查
    
    servo->target_angle = angle;
    servo->pwm_value = Servo_AngleToPWM(angle);
}

// 更新舵机PWM输出
void Servo_Update(Servo_Control *servo) {
    if(!servo->is_enabled) return;
    
    servo->current_angle = Servo_PWMToAngle(servo->pwm_value);
    TIM_SetCompare1(SERVO_TIMER, servo->pwm_value); // 更新PWM占空比
}

// 使能舵机
void Servo_Enable(Servo_Control *servo) {
    servo->is_enabled = 1;
    TIM_Cmd(SERVO_TIMER, ENABLE);
}

// 禁用舵机
void Servo_Disable(Servo_Control *servo) {
    servo->is_enabled = 0;
    TIM_SetCompare1(SERVO_TIMER, 0); // 停止PWM输出
}
