#include "stm32f10x_i2c.h"
#include "stm32f10x_rcc.h"

// I2C初始化
void I2C_Init(I2C_TypeDef* I2Cx, I2C_InitTypeDef* I2C_InitStruct) {
    uint16_t tmpreg = 0, freqrange = 0;
    uint16_t result = 0x04;
    uint32_t pclk1 = 8000000;
    RCC_ClocksTypeDef rcc_clocks;
    
    RCC_GetClocksFreq(&rcc_clocks);
    pclk1 = rcc_clocks.PCLK1_Frequency;
    
    // 获取I2Cx CR2值
    tmpreg = I2Cx->CR2;
    tmpreg &= (uint16_t)~((uint16_t)0x003F); // 清除频率FREQ[5:0]位
    freqrange = (uint16_t)(pclk1 / 1000000);
    tmpreg |= freqrange;
    I2Cx->CR2 = tmpreg;
    
    // 禁用选中的I2C外设以配置时间
    I2Cx->CR1 &= (uint16_t)~((uint16_t)0x0001);
    
    // 获取I2Cx CCR值
    tmpreg = 0;
    
    if (I2C_InitStruct->I2C_ClockSpeed <= 100000) {
        // 标准模式速度计算
        result = (uint16_t)(pclk1 / (I2C_InitStruct->I2C_ClockSpeed << 1));
        if (result < 0x04) {
            result = 0x04;
        }
        tmpreg |= result;
        I2Cx->TRISE = freqrange + 1;
    } else {
        // 快速模式速度计算
        if (I2C_InitStruct->I2C_DutyCycle == I2C_DutyCycle_2) {
            result = (uint16_t)(pclk1 / (I2C_InitStruct->I2C_ClockSpeed * 3));
        } else {
            result = (uint16_t)(pclk1 / (I2C_InitStruct->I2C_ClockSpeed * 25));
            result |= I2C_DutyCycle_16_9;
        }
        
        if ((result & (uint16_t)0x0FFF) == 0) {
            result |= (uint16_t)0x0001;
        }
        tmpreg |= (uint16_t)((uint16_t)0x8000 | result);
        I2Cx->TRISE = (uint16_t)(((freqrange * (uint16_t)300) / (uint16_t)1000) + (uint16_t)1);
    }
    
    I2Cx->CCR = tmpreg;
    
    // 配置I2Cx: 模式和应答
    tmpreg = 0;
    tmpreg |= (uint16_t)((uint32_t)I2C_InitStruct->I2C_Mode | I2C_InitStruct->I2C_Ack);
    I2Cx->CR1 = tmpreg;
    
    // 配置I2Cx OAR1和OAR2
    I2Cx->OAR1 = (I2C_InitStruct->I2C_AcknowledgedAddress | I2C_InitStruct->I2C_OwnAddress1);
}

// 使能或禁用I2C
void I2C_Cmd(I2C_TypeDef* I2Cx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        I2Cx->CR1 |= 0x0001;
    } else {
        I2Cx->CR1 &= (uint16_t)~((uint16_t)0x0001);
    }
}

// 产生I2C起始条件
void I2C_GenerateSTART(I2C_TypeDef* I2Cx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        I2Cx->CR1 |= 0x0100;
    } else {
        I2Cx->CR1 &= (uint16_t)~((uint16_t)0x0100);
    }
}

// 产生I2C停止条件
void I2C_GenerateSTOP(I2C_TypeDef* I2Cx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        I2Cx->CR1 |= 0x0200;
    } else {
        I2Cx->CR1 &= (uint16_t)~((uint16_t)0x0200);
    }
}

// 使能或禁用I2C应答
void I2C_AcknowledgeConfig(I2C_TypeDef* I2Cx, FunctionalState NewState) {
    if (NewState != DISABLE) {
        I2Cx->CR1 |= 0x0400;
    } else {
        I2Cx->CR1 &= (uint16_t)~((uint16_t)0x0400);
    }
}

// 发送7位地址
void I2C_Send7bitAddress(I2C_TypeDef* I2Cx, uint8_t Address, uint8_t I2C_Direction) {
    if (I2C_Direction != I2C_Direction_Transmitter) {
        Address |= 0x01;
    } else {
        Address &= (uint8_t)~((uint8_t)0x01);
    }
    I2Cx->DR = Address;
}

// 发送数据
void I2C_SendData(I2C_TypeDef* I2Cx, uint8_t Data) {
    I2Cx->DR = Data;
}

// 接收数据
uint8_t I2C_ReceiveData(I2C_TypeDef* I2Cx) {
    return (uint8_t)I2Cx->DR;
}

// 检查事件
ErrorStatus I2C_CheckEvent(I2C_TypeDef* I2Cx, uint32_t I2C_EVENT) {
    uint32_t lastevent = 0;
    uint32_t flag1 = 0, flag2 = 0;
    ErrorStatus status = ERROR;
    
    flag1 = I2Cx->SR1;
    flag2 = I2Cx->SR2;
    flag2 = flag2 << 16;
    
    lastevent = (flag1 | flag2) & ((uint32_t)0x00FFFFFF);
    
    if ((lastevent & I2C_EVENT) == I2C_EVENT) {
        status = SUCCESS;
    } else {
        status = ERROR;
    }
    
    return status;
}

// 获取标志状态
FlagStatus I2C_GetFlagStatus(I2C_TypeDef* I2Cx, uint32_t I2C_FLAG) {
    FlagStatus bitstatus = RESET;
    __IO uint32_t i2creg = 0, i2cxbase = 0;
    
    i2cxbase = (uint32_t)I2Cx;
    i2creg = I2C_FLAG >> 28;
    I2C_FLAG &= ((uint32_t)0x00FFFFFF);
    
    if(i2creg != 0) {
        i2creg = (uint32_t)(i2cxbase + 0x14);
    } else {
        I2C_FLAG = (uint32_t)(I2C_FLAG >> 16);
        i2creg = (uint32_t)(i2cxbase + 0x18);
    }
    
    if(((*(__IO uint32_t *)i2creg) & I2C_FLAG) != (uint32_t)RESET) {
        bitstatus = SET;
    } else {
        bitstatus = RESET;
    }
    
    return bitstatus;
}

// I2C结构体初始化
void I2C_StructInit(I2C_InitTypeDef* I2C_InitStruct) {
    I2C_InitStruct->I2C_ClockSpeed = 5000;
    I2C_InitStruct->I2C_Mode = I2C_Mode_I2C;
    I2C_InitStruct->I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStruct->I2C_OwnAddress1 = 0;
    I2C_InitStruct->I2C_Ack = I2C_Ack_Disable;
    I2C_InitStruct->I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
}

// I2C复位
void I2C_DeInit(I2C_TypeDef* I2Cx) {
    if (I2Cx == I2C1) {
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_I2C1, ENABLE);
        RCC_APB1PeriphResetCmd(RCC_APB1Periph_I2C1, DISABLE);
    }
}
